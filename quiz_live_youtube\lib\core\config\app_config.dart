/// Application configuration settings
class AppConfig {
  // YouTube API Configuration
  static const String youtubeClientId = String.fromEnvironment(
    'YOUTUBE_CLIENT_ID',
    defaultValue: 'YOUR_YOUTUBE_CLIENT_ID',
  );
  
  static const String youtubeClientSecret = String.fromEnvironment(
    'YOUTUBE_CLIENT_SECRET',
    defaultValue: 'YOUR_YOUTUBE_CLIENT_SECRET',
  );
  
  // Development/Production flags
  static const bool isDebugMode = bool.fromEnvironment(
    'DEBUG_MODE',
    defaultValue: true,
  );
  
  static const bool enableLogging = bool.fromEnvironment(
    'ENABLE_LOGGING',
    defaultValue: true,
  );
  
  // Stream Configuration
  static const String defaultStreamQuality = String.fromEnvironment(
    'DEFAULT_STREAM_QUALITY',
    defaultValue: '720p',
  );
  
  static const int defaultStreamBitrate = int.fromEnvironment(
    'DEFAULT_STREAM_BITRATE',
    defaultValue: 2500000,
  );
  
  // Chat Configuration
  static const int chatPollIntervalMs = int.fromEnvironment(
    'CHAT_POLL_INTERVAL_MS',
    defaultValue: 2000,
  );
  
  static const int maxChatHistorySize = int.fromEnvironment(
    'MAX_CHAT_HISTORY_SIZE',
    defaultValue: 100,
  );
  
  // Database Configuration
  static const String databaseName = String.fromEnvironment(
    'DATABASE_NAME',
    defaultValue: 'quiz_live.db',
  );
  
  static const int databaseVersion = int.fromEnvironment(
    'DATABASE_VERSION',
    defaultValue: 1,
  );
  
  /// Validate configuration
  static bool get isConfigurationValid {
    return youtubeClientId != 'YOUR_YOUTUBE_CLIENT_ID' &&
           youtubeClientId.isNotEmpty;
  }
  
  /// Get configuration summary for debugging
  static Map<String, dynamic> getConfigSummary() {
    return {
      'youtubeClientIdSet': youtubeClientId != 'YOUR_YOUTUBE_CLIENT_ID',
      'isDebugMode': isDebugMode,
      'enableLogging': enableLogging,
      'defaultStreamQuality': defaultStreamQuality,
      'defaultStreamBitrate': defaultStreamBitrate,
      'chatPollIntervalMs': chatPollIntervalMs,
      'maxChatHistorySize': maxChatHistorySize,
      'databaseName': databaseName,
      'databaseVersion': databaseVersion,
    };
  }
}
