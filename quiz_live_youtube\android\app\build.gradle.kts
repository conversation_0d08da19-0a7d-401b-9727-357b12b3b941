plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.example.quiz_live_youtube"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.example.quiz_live_youtube"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 24  // Required for MediaProjection and FFmpeg Kit
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName

        ndk {
            abiFilters += listOf("arm64-v8a", "armeabi-v7a", "x86_64")
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")
        }
    }
}

dependencies {
    // FFmpeg Kit for video processing and streaming - Version mrljdx
    implementation("com.mrljdx:ffmpeg-kit-full:6.0")

    // Additional dependencies for media handling
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.appcompat:appcompat:1.6.1")
}

// Configuration pour forcer l'utilisation de la version mrljdx
configurations.all {
    resolutionStrategy {
        // Forcer l'utilisation de la version mrljdx pour tous les modules FFmpeg Kit
        force("com.mrljdx:ffmpeg-kit-full:6.0")

        // Remplacer toutes les dépendances arthenica par celles de mrljdx
        dependencySubstitution {
            substitute(module("com.arthenica:ffmpeg-kit-android-full"))
                .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
            substitute(module("com.arthenica:ffmpeg-kit-https"))
                .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
            substitute(module("com.arthenica:ffmpeg-kit-min"))
                .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
            substitute(module("com.arthenica:ffmpeg-kit-audio"))
                .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
            substitute(module("com.arthenica:ffmpeg-kit-video"))
                .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
        }
    }
}

flutter {
    source = "../.."
}
