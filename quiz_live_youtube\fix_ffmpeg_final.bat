@echo off
echo 🔧 Fix Final FFmpeg Kit Maven (mrljdx)
echo =====================================

echo.
echo 📋 Configuration appliquée:
echo - Substitution globale dans android/build.gradle.kts
echo - Force resolution dans gradle.properties
echo - Package: com.mrljdx:ffmpeg-kit-full:6.0
echo.

echo 📋 Étape 1: Nettoyage complet...
echo ----------------------------------------

REM Supprimer les dossiers de build
echo Suppression des dossiers de build...
if exist "build" rmdir /s /q "build"
if exist "android\build" rmdir /s /q "android\build"
if exist "android\app\build" rmdir /s /q "android\app\build"
if exist ".dart_tool" rmdir /s /q ".dart_tool"

REM Nettoyer Flutter
echo Nettoyage Flutter...
flutter clean
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors du nettoyage Flutter
    pause
    exit /b 1
)

REM Nettoyer le cache Pub
echo Nettoyage du cache Pub...
flutter pub cache clean

echo.
echo 📋 Étape 2: Nettoyage Gradle...
echo ----------------------------------------

cd android
if %ERRORLEVEL% neq 0 (
    echo ❌ Dossier android non trouvé
    pause
    exit /b 1
)

REM Supprimer le cache Gradle local
if exist ".gradle" rmdir /s /q ".gradle"

REM Nettoyer Gradle
echo Nettoyage Gradle...
gradlew clean
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors du nettoyage Gradle
    cd ..
    pause
    exit /b 1
)

REM Rafraîchir les dépendances avec force
echo Rafraîchissement forcé des dépendances...
gradlew --refresh-dependencies --recompile-scripts
if %ERRORLEVEL% neq 0 (
    echo ⚠️  Avertissement: Erreur lors du rafraîchissement
)

cd ..

echo.
echo 📋 Étape 3: Installation Flutter...
echo ----------------------------------------

echo Installation des dépendances Flutter...
flutter pub get
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors de l'installation Flutter
    pause
    exit /b 1
)

echo.
echo 📋 Étape 4: Test de résolution des dépendances...
echo ----------------------------------------

cd android
echo Vérification des dépendances résolues...
gradlew app:dependencies --configuration debugRuntimeClasspath | findstr ffmpeg
if %ERRORLEVEL% neq 0 (
    echo ⚠️  Aucune dépendance FFmpeg trouvée dans les logs
)

cd ..

echo.
echo 📋 Étape 5: Test de build...
echo ----------------------------------------

echo Test du build Android (debug)...
flutter build apk --debug --verbose
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors du build Android
    echo.
    echo 🔍 Diagnostics:
    echo 1. Vérifiez les logs ci-dessus pour les erreurs de résolution
    echo 2. Vérifiez que Maven Central est accessible
    echo 3. Consultez android/build.gradle.kts pour la configuration
    echo.
    
    echo 📋 Tentative de diagnostic...
    cd android
    echo Dépendances du module app:
    gradlew app:dependencies --configuration debugCompileClasspath | findstr -i "ffmpeg\|arthenica\|mrljdx"
    
    echo.
    echo Dépendances du module ffmpeg_kit_flutter:
    gradlew :ffmpeg_kit_flutter:dependencies --configuration debugCompileClasspath | findstr -i "ffmpeg\|arthenica\|mrljdx"
    
    cd ..
    pause
    exit /b 1
)

echo.
echo ✅ Build Android réussi !
echo =========================

echo.
echo 📋 Étape 6: Vérification finale...
echo ----------------------------------------

echo Test de l'installation FFmpeg Kit...
dart verify_ffmpeg_maven.dart
if %ERRORLEVEL% neq 0 (
    echo ⚠️  Test de vérification échoué (normal si pas d'émulateur)
)

echo.
echo 🎉 Configuration FFmpeg Kit (mrljdx) terminée avec succès !
echo ===========================================================
echo.
echo 📦 Package utilisé: com.mrljdx:ffmpeg-kit-full:6.0
echo 🔗 Source: https://repo1.maven.org/maven2/com/mrljdx/ffmpeg-kit-full/6.0/
echo 📋 Configuration: Substitution globale dans build.gradle.kts
echo.
echo 🎯 Prochaines étapes:
echo 1. Testez l'application: flutter run
echo 2. Vérifiez les fonctionnalités de streaming
echo 3. Consultez les logs pour confirmer l'utilisation de mrljdx
echo.
echo 📄 Fichiers modifiés:
echo - android/build.gradle.kts (substitution globale)
echo - android/gradle.properties (optimisations)
echo - pubspec.yaml (versions fixes)
echo.

pause
