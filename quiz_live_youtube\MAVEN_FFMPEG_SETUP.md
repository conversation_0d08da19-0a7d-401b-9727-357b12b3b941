# Configuration FFmpeg Kit avec Maven Central (mrljdx)

## Vue d'ensemble

Le package `ffmpeg-kit` de mrljdx est disponible sur Maven Central, ce qui simplifie grandement l'installation par rapport à l'utilisation du repository Git.

**Repository Maven :** https://repo1.maven.org/maven2/com/mrljdx/ffmpeg-kit-full/6.0/

## Configuration Standard (Recommandée)

### 1. pubspec.yaml

```yaml
dependencies:
  ffmpeg_kit_flutter: ^6.0.3
```

Cette configuration utilise le package Flutter standard qui est compatible avec les binaires Maven de mrljdx.

### 2. Configuration Android Spécifique

Si vous voulez utiliser spécifiquement les binaires de mrljdx, ajoutez dans `android/app/build.gradle.kts` :

```kotlin
android {
    dependencies {
        // Utiliser spécifiquement la version mrljdx
        implementation("com.mrljdx:ffmpeg-kit-full:6.0") {
            exclude(group = "com.arthenica", module = "ffmpeg-kit-android-full")
        }
    }
}
```

## Avantages de la Version Maven

### 1. Installation Simplifiée
- ✅ Pas de dépendance Git
- ✅ Résolution automatique des dépendances
- ✅ Cache Maven local
- ✅ Builds plus rapides

### 2. Stabilité
- ✅ Version figée et testée
- ✅ Pas de changements inattendus
- ✅ Compatible avec CI/CD

### 3. Performance
- ✅ Téléchargement plus rapide
- ✅ Cache partagé entre projets
- ✅ Builds offline possibles

## Comparaison des Approches

| Aspect | Git Repository | Maven Central |
|--------|----------------|---------------|
| **Installation** | Complexe | Simple |
| **Stabilité** | Variable | Stable |
| **Performance** | Lente | Rapide |
| **Offline** | Non | Oui (avec cache) |
| **CI/CD** | Problématique | Optimal |
| **Maintenance** | Manuelle | Automatique |

## Configuration Détaillée

### 1. Flutter (pubspec.yaml)

```yaml
name: quiz_live_youtube
description: Quiz Live YouTube with FFmpeg streaming

dependencies:
  flutter:
    sdk: flutter
  
  # FFmpeg Kit - Version compatible Maven
  ffmpeg_kit_flutter: ^6.0.3
  
  # Autres dépendances...
  permission_handler: ^11.3.1
```

### 2. Android (android/app/build.gradle.kts)

```kotlin
android {
    compileSdk 34
    
    defaultConfig {
        applicationId "com.example.quiz_live_youtube"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"
    }
    
    dependencies {
        implementation("androidx.core:core-ktx:1.12.0")
        
        // FFmpeg Kit - Version mrljdx si nécessaire
        // implementation("com.mrljdx:ffmpeg-kit-full:6.0")
    }
}
```

### 3. iOS (ios/Podfile)

```ruby
platform :ios, '11.0'

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  
  # FFmpeg Kit sera automatiquement inclus via Flutter
end
```

## Packages Disponibles

### mrljdx Maven Packages

| Package | Description | Taille |
|---------|-------------|--------|
| `ffmpeg-kit-min` | Fonctionnalités de base | ~20MB |
| `ffmpeg-kit-https` | Avec support HTTPS | ~25MB |
| `ffmpeg-kit-audio` | Avec codecs audio | ~30MB |
| `ffmpeg-kit-video` | Avec codecs vidéo | ~40MB |
| `ffmpeg-kit-full` | Toutes fonctionnalités | ~60MB |

### Configuration par Package

```yaml
# Pour un package minimal
dependencies:
  ffmpeg_kit_flutter_min: ^6.0.3

# Pour le support HTTPS
dependencies:
  ffmpeg_kit_flutter_https: ^6.0.3

# Pour le package complet
dependencies:
  ffmpeg_kit_flutter: ^6.0.3  # (full par défaut)
```

## Installation et Test

### 1. Installation

```bash
# Nettoyer le projet
flutter clean

# Installer les dépendances
flutter pub get

# Pour Android, synchroniser Gradle
cd android && ./gradlew build && cd ..
```

### 2. Test d'Installation

Créez `test_maven_ffmpeg.dart` :

```dart
import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter/return_code.dart';

void main() async {
  print('🔧 Test FFmpeg Kit (Maven)');
  
  try {
    final session = await FFmpegKit.execute('-version');
    final returnCode = await session.getReturnCode();
    
    if (ReturnCode.isSuccess(returnCode)) {
      print('✅ FFmpeg Kit Maven fonctionne !');
      
      final logs = await session.getAllLogs();
      if (logs.isNotEmpty) {
        final version = logs.first.getMessage().split('\n').first;
        print('📄 Version: $version');
      }
    } else {
      print('❌ Échec du test FFmpeg Kit');
    }
  } catch (e) {
    print('❌ Erreur: $e');
  }
}
```

Exécutez :
```bash
dart test_maven_ffmpeg.dart
```

## Optimisations

### 1. Réduction de Taille

```yaml
# Utilisez le package minimal si possible
dependencies:
  ffmpeg_kit_flutter_min: ^6.0.3
```

### 2. Configuration ProGuard (Android)

```kotlin
// android/app/proguard-rules.pro
-keep class com.arthenica.ffmpegkit.** { *; }
-keep class com.mrljdx.** { *; }
```

### 3. Cache Gradle

```kotlin
// android/gradle.properties
org.gradle.caching=true
org.gradle.parallel=true
org.gradle.configureondemand=true
```

## Dépannage

### 1. Problème de Résolution

```bash
# Nettoyer le cache Gradle
cd android
./gradlew clean
./gradlew --refresh-dependencies
cd ..

# Nettoyer Flutter
flutter clean
flutter pub get
```

### 2. Conflit de Versions

```yaml
# Forcer une version spécifique
dependency_overrides:
  ffmpeg_kit_flutter: ^6.0.3
```

### 3. Problème de Build Android

```kotlin
// android/app/build.gradle.kts
android {
    packagingOptions {
        pickFirst("**/libc++_shared.so")
        pickFirst("**/libjsc.so")
    }
}
```

## Migration depuis Git

Si vous migrez depuis la configuration Git :

### 1. Sauvegarder

```bash
cp pubspec.yaml pubspec.yaml.backup
```

### 2. Modifier pubspec.yaml

```yaml
# Remplacer
ffmpeg_kit_flutter:
  git:
    url: https://github.com/mrljdx/ffmpeg-kit.git
    path: flutter/flutter

# Par
ffmpeg_kit_flutter: ^6.0.3
```

### 3. Nettoyer et Réinstaller

```bash
flutter clean
flutter pub get
```

## Avantages Spécifiques mrljdx

La version de mrljdx peut inclure :
- ✅ Optimisations spécifiques
- ✅ Corrections de bugs
- ✅ Fonctionnalités additionnelles
- ✅ Support étendu

## Conclusion

L'utilisation de Maven Central pour FFmpeg Kit offre :
- **Simplicité** : Installation standard Flutter
- **Fiabilité** : Packages testés et stables
- **Performance** : Builds plus rapides
- **Maintenance** : Gestion automatique des dépendances

Cette approche est **recommandée** pour la plupart des projets, sauf si vous avez des besoins très spécifiques nécessitant la version Git.
