import 'package:flutter/material.dart';
import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter/return_code.dart';

/// Script de test pour vérifier l'installation de FFmpeg Kit
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🔧 Test d\'installation FFmpeg Kit');
  print('=====================================');
  
  try {
    // Test 1: Vérifier la version FFmpeg
    print('\n📋 Test 1: Vérification de la version FFmpeg...');
    await testFFmpegVersion();
    
    // Test 2: Test de commande simple
    print('\n📋 Test 2: Test de commande simple...');
    await testSimpleCommand();
    
    // Test 3: Test de génération de commande RTMP
    print('\n📋 Test 3: Test de génération de commande RTMP...');
    testRTMPCommand();
    
    print('\n✅ Tous les tests sont passés avec succès !');
    print('FFmpeg Kit est correctement installé et fonctionnel.');
    
  } catch (e) {
    print('\n❌ Erreur lors des tests: $e');
    print('Vérifiez l\'installation de FFmpeg Kit.');
  }
}

/// Test de la version FFmpeg
Future<void> testFFmpegVersion() async {
  try {
    final session = await FFmpegKit.execute('-version');
    final returnCode = await session.getReturnCode();
    
    if (ReturnCode.isSuccess(returnCode)) {
      print('   ✅ FFmpeg est accessible');
      
      // Récupérer les logs pour voir la version
      final logs = await session.getAllLogs();
      if (logs.isNotEmpty) {
        final versionLine = logs.first.getMessage();
        print('   📄 Version: ${versionLine.split('\n').first}');
      }
    } else {
      print('   ❌ Échec de l\'exécution de FFmpeg');
    }
  } catch (e) {
    print('   ❌ Erreur: $e');
    rethrow;
  }
}

/// Test d'une commande simple
Future<void> testSimpleCommand() async {
  try {
    // Test avec une commande qui liste les codecs disponibles
    final session = await FFmpegKit.execute('-codecs');
    final returnCode = await session.getReturnCode();
    
    if (ReturnCode.isSuccess(returnCode)) {
      print('   ✅ Commande -codecs exécutée avec succès');
      
      // Compter les codecs disponibles
      final logs = await session.getAllLogs();
      int codecCount = 0;
      for (final log in logs) {
        if (log.getMessage().contains('DEV')) {
          codecCount++;
        }
      }
      print('   📊 Nombre de codecs détectés: $codecCount');
    } else {
      print('   ❌ Échec de la commande -codecs');
    }
  } catch (e) {
    print('   ❌ Erreur: $e');
    rethrow;
  }
}

/// Test de génération de commande RTMP
void testRTMPCommand() {
  try {
    final command = buildTestRTMPCommand();
    print('   ✅ Commande RTMP générée avec succès');
    print('   📝 Commande: $command');
    
    // Vérifier que la commande contient les éléments essentiels
    final requiredElements = ['-f', 'lavfi', '-i', 'testsrc', '-c:v', 'libx264', '-f', 'flv'];
    bool allElementsPresent = true;
    
    for (final element in requiredElements) {
      if (!command.contains(element)) {
        print('   ⚠️  Élément manquant: $element');
        allElementsPresent = false;
      }
    }
    
    if (allElementsPresent) {
      print('   ✅ Tous les éléments requis sont présents');
    } else {
      print('   ❌ Certains éléments requis sont manquants');
    }
    
  } catch (e) {
    print('   ❌ Erreur lors de la génération: $e');
    rethrow;
  }
}

/// Génère une commande RTMP de test
String buildTestRTMPCommand() {
  final rtmpUrl = 'rtmp://test-server.com/live/test-key';
  final width = 1280;
  final height = 720;
  final frameRate = 30;
  final bitrate = 2500;
  
  final List<String> args = [
    // Source de test
    '-f', 'lavfi',
    '-i', 'testsrc=size=${width}x$height:rate=$frameRate',
    
    // Encodage vidéo
    '-c:v', 'libx264',
    '-b:v', '${bitrate}k',
    '-maxrate', '${bitrate}k',
    '-bufsize', '${bitrate * 2}k',
    '-preset', 'ultrafast',
    '-tune', 'zerolatency',
    
    // Audio de test
    '-f', 'lavfi',
    '-i', 'anullsrc=channel_layout=stereo:sample_rate=44100',
    '-c:a', 'aac',
    '-b:a', '128k',
    
    // Sortie RTMP
    '-f', 'flv',
    rtmpUrl,
  ];
  
  return args.join(' ');
}

/// Classe de test pour le service de streaming
class FFmpegTestService {
  /// Test d'initialisation du service
  static Future<bool> testServiceInitialization() async {
    try {
      print('\n🔧 Test d\'initialisation du service...');
      
      // Simuler l'initialisation
      print('   📋 Configuration des callbacks...');
      FFmpegKit.enableLogCallback((log) {
        // Callback de test
      });
      
      print('   ✅ Service initialisé avec succès');
      return true;
    } catch (e) {
      print('   ❌ Erreur d\'initialisation: $e');
      return false;
    }
  }
  
  /// Test de construction de commande
  static bool testCommandBuilding() {
    try {
      print('\n🔧 Test de construction de commande...');
      
      final command = buildTestRTMPCommand();
      
      // Vérifications
      if (command.isEmpty) {
        print('   ❌ Commande vide');
        return false;
      }
      
      if (!command.contains('rtmp://')) {
        print('   ❌ URL RTMP manquante');
        return false;
      }
      
      if (!command.contains('libx264')) {
        print('   ❌ Codec vidéo manquant');
        return false;
      }
      
      print('   ✅ Commande construite correctement');
      return true;
    } catch (e) {
      print('   ❌ Erreur de construction: $e');
      return false;
    }
  }
}

/// Fonction utilitaire pour exécuter tous les tests
Future<void> runAllTests() async {
  print('🚀 Démarrage de tous les tests FFmpeg Kit');
  print('==========================================');
  
  final results = <String, bool>{};
  
  // Test d'installation de base
  try {
    await testFFmpegVersion();
    results['Version FFmpeg'] = true;
  } catch (e) {
    results['Version FFmpeg'] = false;
  }
  
  try {
    await testSimpleCommand();
    results['Commande simple'] = true;
  } catch (e) {
    results['Commande simple'] = false;
  }
  
  // Test du service
  results['Initialisation service'] = await FFmpegTestService.testServiceInitialization();
  results['Construction commande'] = FFmpegTestService.testCommandBuilding();
  
  // Résumé des résultats
  print('\n📊 Résumé des Tests');
  print('===================');
  
  int passed = 0;
  int total = results.length;
  
  results.forEach((test, success) {
    final status = success ? '✅' : '❌';
    print('$status $test');
    if (success) passed++;
  });
  
  print('\n📈 Résultat: $passed/$total tests réussis');
  
  if (passed == total) {
    print('🎉 Tous les tests sont passés ! FFmpeg Kit est prêt à être utilisé.');
  } else {
    print('⚠️  Certains tests ont échoué. Vérifiez l\'installation.');
  }
}
