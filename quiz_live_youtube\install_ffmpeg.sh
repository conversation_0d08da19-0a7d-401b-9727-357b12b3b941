#!/bin/bash

# Script d'installation FFmpeg Kit pour Quiz Live YouTube
# Usage: ./install_ffmpeg.sh

set -e  # Arrêter en cas d'erreur

echo "🚀 Installation FFmpeg Kit pour Quiz Live YouTube"
echo "================================================="

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages colorés
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier que nous sommes dans le bon répertoire
if [ ! -f "pubspec.yaml" ]; then
    print_error "pubspec.yaml non trouvé. Exécutez ce script depuis la racine du projet Flutter."
    exit 1
fi

print_status "Vérification de l'environnement..."

# Vérifier Flutter
if ! command -v flutter &> /dev/null; then
    print_error "Flutter n'est pas installé ou pas dans le PATH"
    exit 1
fi

print_success "Flutter trouvé: $(flutter --version | head -n 1)"

# Vérifier Git
if ! command -v git &> /dev/null; then
    print_error "Git n'est pas installé ou pas dans le PATH"
    exit 1
fi

print_success "Git trouvé: $(git --version)"

# Vérifier la connectivité au repository
print_status "Vérification de l'accès au repository FFmpeg Kit..."

if git ls-remote https://github.com/mrljdx/ffmpeg-kit.git &> /dev/null; then
    print_success "Repository FFmpeg Kit accessible"
else
    print_error "Impossible d'accéder au repository FFmpeg Kit"
    print_warning "Vérifiez votre connexion internet et l'accès à GitHub"
    exit 1
fi

# Nettoyer le cache Flutter
print_status "Nettoyage du cache Flutter..."
flutter clean > /dev/null 2>&1
flutter pub cache clean > /dev/null 2>&1
print_success "Cache nettoyé"

# Sauvegarder le pubspec.yaml actuel
print_status "Sauvegarde du pubspec.yaml..."
cp pubspec.yaml pubspec.yaml.backup
print_success "Sauvegarde créée: pubspec.yaml.backup"

# Vérifier la configuration FFmpeg Kit dans pubspec.yaml
print_status "Vérification de la configuration FFmpeg Kit..."

if grep -q "ffmpeg_kit_flutter:" pubspec.yaml; then
    if grep -A 3 "ffmpeg_kit_flutter:" pubspec.yaml | grep -q "github.com/mrljdx/ffmpeg-kit"; then
        print_success "Configuration FFmpeg Kit trouvée"
    else
        print_warning "Configuration FFmpeg Kit incorrecte, correction en cours..."
        
        # Corriger la configuration (cette partie nécessiterait un script plus complexe)
        print_warning "Veuillez vérifier manuellement la configuration dans pubspec.yaml"
        print_warning "Elle devrait ressembler à:"
        echo "  ffmpeg_kit_flutter:"
        echo "    git:"
        echo "      url: https://github.com/mrljdx/ffmpeg-kit.git"
        echo "      path: flutter/flutter"
    fi
else
    print_error "Configuration FFmpeg Kit non trouvée dans pubspec.yaml"
    exit 1
fi

# Installer les dépendances
print_status "Installation des dépendances..."

if flutter pub get; then
    print_success "Dépendances installées avec succès"
else
    print_error "Échec de l'installation des dépendances"
    print_warning "Tentative avec le package officiel..."
    
    # Restaurer la sauvegarde
    cp pubspec.yaml.backup pubspec.yaml
    
    # Modifier pour utiliser le package officiel
    sed -i.tmp 's/ffmpeg_kit_flutter:.*/ffmpeg_kit_flutter: ^6.0.3/' pubspec.yaml
    rm pubspec.yaml.tmp 2>/dev/null || true
    
    if flutter pub get; then
        print_warning "Installation réussie avec le package officiel"
        print_warning "Vous utilisez maintenant ffmpeg_kit_flutter: ^6.0.3"
    else
        print_error "Échec même avec le package officiel"
        cp pubspec.yaml.backup pubspec.yaml
        exit 1
    fi
fi

# Test de base
print_status "Test de l'installation..."

# Créer un fichier de test temporaire
cat > test_temp.dart << 'EOF'
import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter/return_code.dart';

void main() async {
  try {
    final session = await FFmpegKit.execute('-version');
    final returnCode = await session.getReturnCode();
    
    if (ReturnCode.isSuccess(returnCode)) {
      print('SUCCESS: FFmpeg Kit fonctionne');
    } else {
      print('ERROR: FFmpeg Kit ne répond pas');
    }
  } catch (e) {
    print('ERROR: $e');
  }
}
EOF

# Tenter d'exécuter le test
if dart test_temp.dart 2>/dev/null | grep -q "SUCCESS"; then
    print_success "Test d'installation réussi"
else
    print_warning "Test d'installation échoué (normal si pas d'émulateur)"
    print_warning "L'installation semble correcte, testez sur un appareil"
fi

# Nettoyer
rm -f test_temp.dart

# Vérifier les imports dans le code
print_status "Vérification des imports dans le code..."

if find lib -name "*.dart" -exec grep -l "ffmpeg_kit_flutter" {} \; | wc -l | grep -q "0"; then
    print_warning "Aucun import FFmpeg Kit trouvé dans le code"
else
    print_success "Imports FFmpeg Kit trouvés dans le code"
fi

# Résumé
echo ""
echo "📋 Résumé de l'Installation"
echo "=========================="

if [ -f pubspec.yaml.backup ]; then
    if diff pubspec.yaml pubspec.yaml.backup > /dev/null; then
        print_success "Configuration inchangée"
    else
        print_warning "Configuration modifiée (sauvegarde disponible)"
    fi
fi

# Vérifier quelle version est installée
if grep -q "git:" pubspec.yaml && grep -q "mrljdx/ffmpeg-kit" pubspec.yaml; then
    print_success "FFmpeg Kit personnalisé installé"
    echo "   📦 Source: Repository GitHub personnalisé"
    echo "   🔗 URL: https://github.com/mrljdx/ffmpeg-kit.git"
elif grep -q "ffmpeg_kit_flutter: \^" pubspec.yaml; then
    print_warning "FFmpeg Kit officiel installé"
    echo "   📦 Source: pub.dev (package officiel)"
    echo "   ⚠️  Vous n'utilisez pas le repository personnalisé demandé"
else
    print_error "Configuration FFmpeg Kit non reconnue"
fi

echo ""
echo "🎯 Prochaines Étapes"
echo "==================="
echo "1. Testez l'application: flutter run"
echo "2. Vérifiez les fonctionnalités de streaming"
echo "3. Consultez TROUBLESHOOTING_FFMPEG.md en cas de problème"
echo "4. Exécutez dart test_ffmpeg_installation.dart pour des tests détaillés"

echo ""
print_success "Installation terminée !"

# Proposer de supprimer la sauvegarde
echo ""
read -p "Supprimer la sauvegarde pubspec.yaml.backup ? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -f pubspec.yaml.backup
    print_success "Sauvegarde supprimée"
else
    print_status "Sauvegarde conservée: pubspec.yaml.backup"
fi

echo ""
echo "🚀 Installation FFmpeg Kit terminée avec succès !"
