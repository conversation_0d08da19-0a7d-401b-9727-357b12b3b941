import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'core/constants/app_constants.dart';
import 'core/providers/service_providers.dart';
import 'core/utils/navigation_utils.dart';
import 'presentation/theme/app_theme.dart';

void main() {
  runApp(const ProviderScope(child: QuizLiveYouTubeApp()));
}

class QuizLiveYouTubeApp extends ConsumerWidget {
  const QuizLiveYouTubeApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      initialRoute: NavigationUtils.splash,
      onGenerateRoute: NavigationUtils.generateRoute,
      debugShowCheckedModeBanner: false,
    );
  }
}


