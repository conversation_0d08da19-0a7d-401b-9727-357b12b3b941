allprojects {
    repositories {
        google()
        mavenCentral()
    }

    // Configuration globale pour forcer l'utilisation de mrljdx
    configurations.all {
        resolutionStrategy {
            // Forcer l'utilisation de la version mrljdx
            force("com.mrljdx:ffmpeg-kit-full:6.0")

            // Substitution globale de toutes les dépendances arthenica
            dependencySubstitution {
                substitute(module("com.arthenica:ffmpeg-kit-android-full"))
                    .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
                substitute(module("com.arthenica:ffmpeg-kit-https"))
                    .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
                substitute(module("com.arthenica:ffmpeg-kit-https:6.0-2"))
                    .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
                substitute(module("com.arthenica:ffmpeg-kit-min"))
                    .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
                substitute(module("com.arthenica:ffmpeg-kit-audio"))
                    .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
                substitute(module("com.arthenica:ffmpeg-kit-video"))
                    .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
                substitute(module("com.arthenica:ffmpeg-kit-full"))
                    .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
            }
        }
    }
}

val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}
