name: quiz_live_youtube
description: "Flutter app for YouTube Live quiz streaming with real-time chat integration."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI and Material Design
  cupertino_icons: ^1.0.8
  material_color_utilities: ^0.11.1

  # State Management
  flutter_riverpod: ^2.5.1

  # HTTP and API
  http: ^1.2.1
  dio: ^5.4.3+1

  # Authentication
  google_sign_in: ^6.2.1
  oauth2: ^2.0.2

  # Local Storage
  sqflite: ^2.3.3+1
  shared_preferences: ^2.2.3
  path_provider: ^2.1.3

  # JSON and Serialization
  json_annotation: ^4.9.0


  permission_handler: ^11.3.1

  # WebSocket for real-time chat
  web_socket_channel: ^2.4.5

  # Utilities
  uuid: ^4.4.0
  intl: ^0.19.0
  logger: ^2.3.0

  # UI Components
  flutter_animate: ^4.5.0
  lottie: ^3.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.9
  json_serializable: ^6.8.0

  # Testing
  mockito: ^5.4.4

# Force l'utilisation de versions spécifiques
dependency_overrides:
  # Assurer la compatibilité avec la version mrljdx d'FFmpeg Kit
  ffmpeg_kit_flutter: 6.0.3
  ffmpeg_kit_flutter_https: 6.0.3


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/animations/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
