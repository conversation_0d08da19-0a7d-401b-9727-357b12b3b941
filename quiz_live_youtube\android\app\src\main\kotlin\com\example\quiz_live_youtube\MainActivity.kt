package com.example.quiz_live_youtube

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "quiz_live_youtube/streaming"
    private val REQUEST_CODE_SCREEN_CAPTURE = 1000

    private var mediaProjectionManager: MediaProjectionManager? = null
    private var mediaProjection: MediaProjection? = null
    private var streamingService: StreamingService? = null
    private var pendingResult: MethodChannel.Result? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        streamingService = StreamingService(this)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "initialize" -> {
                    streamingService?.initialize()
                    result.success(null)
                }
                "requestScreenPermission" -> {
                    requestScreenCapturePermission(result)
                }
                "startStreaming" -> {
                    val rtmpUrl = call.argument<String>("rtmpUrl")
                    val width = call.argument<Int>("width") ?: 1280
                    val height = call.argument<Int>("height") ?: 720
                    val frameRate = call.argument<Int>("frameRate") ?: 30
                    val bitrate = call.argument<Int>("bitrate") ?: 2500000
                    val codec = call.argument<String>("codec") ?: "h264"

                    if (rtmpUrl != null) {
                        startStreaming(rtmpUrl, width, height, frameRate, bitrate, codec, result)
                    } else {
                        result.error("INVALID_ARGUMENTS", "RTMP URL is required", null)
                    }
                }
                "stopStreaming" -> {
                    stopStreaming(result)
                }
                "pauseStreaming" -> {
                    pauseStreaming(result)
                }
                "resumeStreaming" -> {
                    resumeStreaming(result)
                }
                "updateSettings" -> {
                    val width = call.argument<Int>("width") ?: 1280
                    val height = call.argument<Int>("height") ?: 720
                    val frameRate = call.argument<Int>("frameRate") ?: 30
                    val bitrate = call.argument<Int>("bitrate") ?: 2500000

                    updateStreamSettings(width, height, frameRate, bitrate, result)
                }
                "getStatus" -> {
                    getStreamingStatus(result)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun requestScreenCapturePermission(result: MethodChannel.Result) {
        pendingResult = result
        val captureIntent = mediaProjectionManager?.createScreenCaptureIntent()
        startActivityForResult(captureIntent, REQUEST_CODE_SCREEN_CAPTURE)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == REQUEST_CODE_SCREEN_CAPTURE) {
            if (resultCode == Activity.RESULT_OK && data != null) {
                mediaProjection = mediaProjectionManager?.getMediaProjection(resultCode, data)
                streamingService?.setMediaProjection(mediaProjection)
                pendingResult?.success(true)
            } else {
                pendingResult?.success(false)
            }
            pendingResult = null
        }
    }

    private fun startStreaming(
        rtmpUrl: String,
        width: Int,
        height: Int,
        frameRate: Int,
        bitrate: Int,
        codec: String,
        result: MethodChannel.Result
    ) {
        if (mediaProjection == null) {
            result.error("NO_PERMISSION", "Screen capture permission not granted", null)
            return
        }

        streamingService?.startStreaming(
            rtmpUrl, width, height, frameRate, bitrate, codec
        ) { success, error ->
            if (success) {
                result.success(mapOf("success" to true))
            } else {
                result.success(mapOf("success" to false, "error" to error))
            }
        }
    }

    private fun stopStreaming(result: MethodChannel.Result) {
        streamingService?.stopStreaming { success, error ->
            if (success) {
                result.success(null)
            } else {
                result.error("STOP_FAILED", error ?: "Failed to stop streaming", null)
            }
        }
    }

    private fun pauseStreaming(result: MethodChannel.Result) {
        streamingService?.pauseStreaming()
        result.success(null)
    }

    private fun resumeStreaming(result: MethodChannel.Result) {
        streamingService?.resumeStreaming()
        result.success(null)
    }

    private fun updateStreamSettings(
        width: Int,
        height: Int,
        frameRate: Int,
        bitrate: Int,
        result: MethodChannel.Result
    ) {
        streamingService?.updateSettings(width, height, frameRate, bitrate)
        result.success(null)
    }

    private fun getStreamingStatus(result: MethodChannel.Result) {
        val status = streamingService?.getStatus()
        result.success(status)
    }

    override fun onDestroy() {
        super.onDestroy()
        streamingService?.cleanup()
        mediaProjection?.stop()
    }
}
