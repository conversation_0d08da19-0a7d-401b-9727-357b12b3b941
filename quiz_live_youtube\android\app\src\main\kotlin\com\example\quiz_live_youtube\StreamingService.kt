package com.example.quiz_live_youtube

import android.content.Context
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.MediaRecorder
import android.media.projection.MediaProjection
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.util.Log
import android.view.Surface
import android.view.WindowManager
import com.arthenica.ffmpegkit.FFmpegKit
import com.arthenica.ffmpegkit.FFmpegSession
import com.arthenica.ffmpegkit.ReturnCode
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

class StreamingService(private val context: Context) {
    companion object {
        private const val TAG = "StreamingService"
        private const val VIRTUAL_DISPLAY_NAME = "QuizLiveYouTube"
    }

    private var mediaProjection: MediaProjection? = null
    private var virtualDisplay: VirtualDisplay? = null
    private var mediaRecorder: MediaRecorder? = null
    private var ffmpegSession: FFmpegSession? = null
    
    private val isStreaming = AtomicBoolean(false)
    private val isPaused = AtomicBoolean(false)
    
    private var currentWidth = 1280
    private var currentHeight = 720
    private var currentFrameRate = 30
    private var currentBitrate = 2500000
    private var currentRtmpUrl: String? = null
    
    private val handler = Handler(Looper.getMainLooper())
    private var statusUpdateRunnable: Runnable? = null
    
    // Streaming statistics
    private var startTime: Long = 0
    private var droppedFrames = 0
    private var actualBitrate = 0
    private var actualFrameRate = 0.0

    fun initialize() {
        Log.d(TAG, "StreamingService initialized")
    }

    fun setMediaProjection(projection: MediaProjection?) {
        this.mediaProjection = projection
        Log.d(TAG, "MediaProjection set: ${projection != null}")
    }

    fun startStreaming(
        rtmpUrl: String,
        width: Int,
        height: Int,
        frameRate: Int,
        bitrate: Int,
        codec: String,
        callback: (Boolean, String?) -> Unit
    ) {
        if (isStreaming.get()) {
            callback(false, "Already streaming")
            return
        }

        if (mediaProjection == null) {
            callback(false, "MediaProjection not available")
            return
        }

        try {
            currentWidth = width
            currentHeight = height
            currentFrameRate = frameRate
            currentBitrate = bitrate
            currentRtmpUrl = rtmpUrl
            
            // Create temporary file for recording
            val tempFile = File(context.cacheDir, "temp_recording.mp4")
            if (tempFile.exists()) {
                tempFile.delete()
            }

            // Setup MediaRecorder
            setupMediaRecorder(tempFile.absolutePath, width, height, frameRate, bitrate)

            // Create VirtualDisplay
            createVirtualDisplay()

            // Start recording
            mediaRecorder?.start()
            
            // Start RTMP streaming with FFmpeg
            startRtmpStreaming(tempFile.absolutePath, rtmpUrl)
            
            isStreaming.set(true)
            startTime = System.currentTimeMillis()
            startStatusUpdates()
            
            Log.d(TAG, "Streaming started successfully")
            callback(true, null)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start streaming", e)
            cleanup()
            callback(false, e.message)
        }
    }

    private fun setupMediaRecorder(outputPath: String, width: Int, height: Int, frameRate: Int, bitrate: Int) {
        mediaRecorder = MediaRecorder().apply {
            setVideoSource(MediaRecorder.VideoSource.SURFACE)
            setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
            setVideoEncoder(MediaRecorder.VideoEncoder.H264)
            setVideoSize(width, height)
            setVideoFrameRate(frameRate)
            setVideoEncodingBitRate(bitrate)
            setOutputFile(outputPath)
            prepare()
        }
    }

    private fun createVirtualDisplay() {
        val displayMetrics = getDisplayMetrics()
        
        virtualDisplay = mediaProjection?.createVirtualDisplay(
            VIRTUAL_DISPLAY_NAME,
            currentWidth,
            currentHeight,
            displayMetrics.densityDpi,
            DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
            mediaRecorder?.surface,
            null,
            null
        )
    }

    private fun startRtmpStreaming(inputPath: String, rtmpUrl: String) {
        // FFmpeg command for RTMP streaming
        val command = arrayOf(
            "-re",
            "-i", inputPath,
            "-c:v", "libx264",
            "-preset", "ultrafast",
            "-tune", "zerolatency",
            "-crf", "23",
            "-maxrate", "${currentBitrate}",
            "-bufsize", "${currentBitrate * 2}",
            "-pix_fmt", "yuv420p",
            "-g", "${currentFrameRate * 2}",
            "-f", "flv",
            rtmpUrl
        )

        ffmpegSession = FFmpegKit.executeAsync(
            command.joinToString(" ")
        ) { session ->
            val returnCode = session.returnCode
            if (ReturnCode.isSuccess(returnCode)) {
                Log.d(TAG, "FFmpeg streaming completed successfully")
            } else {
                Log.e(TAG, "FFmpeg streaming failed with return code: $returnCode")
                Log.e(TAG, "FFmpeg output: ${session.output}")
            }
        }
    }

    fun stopStreaming(callback: (Boolean, String?) -> Unit) {
        try {
            isStreaming.set(false)
            stopStatusUpdates()
            
            // Stop FFmpeg session
            ffmpegSession?.cancel()
            ffmpegSession = null
            
            // Stop MediaRecorder
            mediaRecorder?.apply {
                try {
                    stop()
                } catch (e: Exception) {
                    Log.w(TAG, "Error stopping MediaRecorder", e)
                }
                release()
            }
            mediaRecorder = null
            
            // Stop VirtualDisplay
            virtualDisplay?.release()
            virtualDisplay = null
            
            Log.d(TAG, "Streaming stopped successfully")
            callback(true, null)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop streaming", e)
            callback(false, e.message)
        }
    }

    fun pauseStreaming() {
        if (isStreaming.get() && !isPaused.get()) {
            isPaused.set(true)
            // Pause FFmpeg session if possible
            Log.d(TAG, "Streaming paused")
        }
    }

    fun resumeStreaming() {
        if (isStreaming.get() && isPaused.get()) {
            isPaused.set(false)
            // Resume FFmpeg session if possible
            Log.d(TAG, "Streaming resumed")
        }
    }

    fun updateSettings(width: Int, height: Int, frameRate: Int, bitrate: Int) {
        currentWidth = width
        currentHeight = height
        currentFrameRate = frameRate
        currentBitrate = bitrate
        
        Log.d(TAG, "Settings updated: ${width}x${height}, ${frameRate}fps, ${bitrate}bps")
    }

    fun getStatus(): Map<String, Any> {
        val currentTime = System.currentTimeMillis()
        val streamDuration = if (startTime > 0) currentTime - startTime else 0
        
        return mapOf(
            "isStreaming" to isStreaming.get(),
            "isPaused" to isPaused.get(),
            "bitrate" to actualBitrate,
            "frameRate" to actualFrameRate,
            "droppedFrames" to droppedFrames,
            "streamDuration" to streamDuration,
            "width" to currentWidth,
            "height" to currentHeight
        )
    }

    private fun startStatusUpdates() {
        statusUpdateRunnable = object : Runnable {
            override fun run() {
                if (isStreaming.get()) {
                    updateStreamingStats()
                    handler.postDelayed(this, 1000) // Update every second
                }
            }
        }
        handler.post(statusUpdateRunnable!!)
    }

    private fun stopStatusUpdates() {
        statusUpdateRunnable?.let { handler.removeCallbacks(it) }
        statusUpdateRunnable = null
    }

    private fun updateStreamingStats() {
        // Update streaming statistics
        // This is a simplified implementation - in a real app, you'd get actual stats from FFmpeg
        actualBitrate = currentBitrate
        actualFrameRate = currentFrameRate.toDouble()
        
        // Simulate some dropped frames occasionally
        if (Math.random() < 0.1) {
            droppedFrames++
        }
    }

    private fun getDisplayMetrics(): DisplayMetrics {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        return displayMetrics
    }

    fun cleanup() {
        try {
            stopStreaming { _, _ -> }
            mediaProjection?.stop()
            mediaProjection = null
            Log.d(TAG, "StreamingService cleaned up")
        } catch (e: Exception) {
            Log.e(TAG, "Error during cleanup", e)
        }
    }
}
