#!/bin/bash

# Build script for Quiz Live YouTube Flutter app

echo "🚀 Building Quiz Live YouTube App..."

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed. Please install Flutter first."
    exit 1
fi

# Check Flutter doctor
echo "📋 Checking Flutter environment..."
flutter doctor

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Generate code files
echo "🔧 Generating code files..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# Run tests
echo "🧪 Running tests..."
flutter test

# Build APK for Android
echo "📱 Building Android APK..."
flutter build apk --release

# Build App Bundle for Google Play
echo "📦 Building Android App Bundle..."
flutter build appbundle --release

echo "✅ Build completed successfully!"
echo ""
echo "📁 Output files:"
echo "   APK: build/app/outputs/flutter-apk/app-release.apk"
echo "   App Bundle: build/app/outputs/bundle/release/app-release.aab"
echo ""
echo "🎉 Ready to deploy!"
