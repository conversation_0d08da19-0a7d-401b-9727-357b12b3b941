import 'dart:async';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter/ffmpeg_session.dart';
import 'package:ffmpeg_kit_flutter/return_code.dart';
import 'package:ffmpeg_kit_flutter/session_state.dart';

import '../models/youtube_models.dart';

/// Service for handling screen capture and RTMP streaming using FFmpeg
class StreamingService {
  static final Logger _logger = Logger();

  StreamStatus _currentStatus = const StreamStatus();
  final StreamController<StreamStatus> _statusController =
      StreamController<StreamStatus>.broadcast();

  Timer? _statusTimer;
  bool _isInitialized = false;
  FFmpegSession? _currentSession;
  
  /// Stream of streaming status updates
  Stream<StreamStatus> get statusStream => _statusController.stream;
  
  /// Current streaming status
  StreamStatus get currentStatus => _currentStatus;
  
  /// Check if streaming is active
  bool get isStreaming => _currentStatus.state == StreamState.streaming;
  
  /// Initialize the streaming service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Request necessary permissions
      await _requestPermissions();

      // Initialize FFmpeg Kit
      // Note: Log callback setup would be done here if available in the package
      _logger.i('FFmpeg Kit initialized');

      _isInitialized = true;
      _logger.i('Streaming service initialized with FFmpeg Kit');
    } catch (e) {
      _logger.e('Failed to initialize streaming service: $e');
      rethrow;
    }
  }
  
  /// Request necessary permissions for streaming
  Future<void> _requestPermissions() async {
    final permissions = [
      Permission.microphone,
      Permission.camera,
      Permission.storage,
    ];
    
    final statuses = await permissions.request();
    
    for (final permission in permissions) {
      if (statuses[permission] != PermissionStatus.granted) {
        _logger.w('Permission ${permission.toString()} not granted');
      }
    }
    
    // Note: Screen capture permission needs to be handled at the platform level
    // For Android, this would require MediaProjection API
    // For iOS, this would require ReplayKit
    // FFmpeg Kit doesn't handle screen capture permissions directly
    _logger.i('Screen capture permission should be handled by the platform');
  }
  
  /// Start streaming to YouTube
  Future<void> startStreaming({
    required String rtmpUrl,
    required String streamKey,
    StreamSettings? settings,
  }) async {
    if (!_isInitialized) {
      throw Exception('Streaming service not initialized');
    }
    
    if (isStreaming) {
      throw Exception('Already streaming');
    }
    
    try {
      final streamSettings = settings ?? const StreamSettings();
      final fullRtmpUrl = '$rtmpUrl/$streamKey';
      
      _logger.i('Starting stream to: $fullRtmpUrl');
      
      // Build FFmpeg command for RTMP streaming
      // Note: This is a basic example. For screen capture, you would need
      // to implement platform-specific screen capture and pipe it to FFmpeg
      final command = _buildFFmpegCommand(fullRtmpUrl, streamSettings);

      _logger.i('Starting FFmpeg with command: $command');

      // Execute FFmpeg command
      _currentSession = await FFmpegKit.executeAsync(
        command,
        (session) async {
          final returnCode = await session.getReturnCode();
          if (ReturnCode.isSuccess(returnCode)) {
            _logger.i('FFmpeg session completed successfully');
            _updateStatus(_currentStatus.copyWith(
              state: StreamState.disconnected,
              lastUpdate: DateTime.now(),
            ));
          } else {
            _logger.e('FFmpeg session failed with return code: $returnCode');
            _updateStatus(_currentStatus.copyWith(
              state: StreamState.error,
              error: 'FFmpeg session failed',
              lastUpdate: DateTime.now(),
            ));
          }
        },
        (log) {
          _logger.d('FFmpeg Log: ${log.getMessage()}');
        },
        (statistics) {
          // Update streaming statistics
          _logger.d('FFmpeg Stats: ${statistics.toString()}');
        },
      );

      _updateStatus(_currentStatus.copyWith(
        state: StreamState.connecting,
        lastUpdate: DateTime.now(),
      ));

      // Start status monitoring
      _startStatusMonitoring();

      _logger.i('Stream started successfully');
    } catch (e) {
      _logger.e('Failed to start streaming: $e');
      _updateStatus(_currentStatus.copyWith(
        state: StreamState.error,
        error: e.toString(),
        lastUpdate: DateTime.now(),
      ));
      rethrow;
    }
  }
  
  /// Stop streaming
  Future<void> stopStreaming() async {
    if (!isStreaming && _currentStatus.state != StreamState.connecting) {
      return;
    }
    
    try {
      // Cancel current FFmpeg session
      if (_currentSession != null) {
        await _currentSession!.cancel();
        _currentSession = null;
      }
      
      _stopStatusMonitoring();
      
      _updateStatus(_currentStatus.copyWith(
        state: StreamState.disconnected,
        bitrate: 0,
        frameRate: 0,
        droppedFrames: 0,
        error: null,
        lastUpdate: DateTime.now(),
      ));
      
      _logger.i('Stream stopped successfully');
    } catch (e) {
      _logger.e('Failed to stop streaming: $e');
      rethrow;
    }
  }
  
  /// Pause streaming
  Future<void> pauseStreaming() async {
    if (!isStreaming) return;
    
    try {
      // FFmpeg doesn't support pause/resume directly
      // We would need to stop and restart the stream
      _logger.w('Pause/Resume not directly supported with FFmpeg. Consider stopping and restarting.');
      _logger.i('Stream paused');
    } catch (e) {
      _logger.e('Failed to pause streaming: $e');
      rethrow;
    }
  }
  
  /// Resume streaming
  Future<void> resumeStreaming() async {
    try {
      // FFmpeg doesn't support pause/resume directly
      // We would need to stop and restart the stream
      _logger.w('Pause/Resume not directly supported with FFmpeg. Consider stopping and restarting.');
      _logger.i('Stream resumed');
    } catch (e) {
      _logger.e('Failed to resume streaming: $e');
      rethrow;
    }
  }
  
  /// Update stream settings during streaming
  Future<void> updateStreamSettings(StreamSettings settings) async {
    if (!isStreaming) return;
    
    try {
      // With FFmpeg, we need to stop and restart with new settings
      _logger.i('To update settings with FFmpeg, stop and restart the stream');
      
      _logger.i('Stream settings updated');
    } catch (e) {
      _logger.e('Failed to update stream settings: $e');
      rethrow;
    }
  }
  
  /// Build FFmpeg command for RTMP streaming
  String _buildFFmpegCommand(String rtmpUrl, StreamSettings settings) {
    // Basic FFmpeg command for RTMP streaming
    // Note: This is a simplified example. For screen capture, you would need:
    // - On Android: Use MediaProjection API to capture screen
    // - On iOS: Use ReplayKit to capture screen
    // - Pipe the captured video to FFmpeg

    final List<String> args = [
      // Input settings (this would be replaced with actual screen capture input)
      '-f', 'lavfi',
      '-i', 'testsrc=size=${settings.width}x${settings.height}:rate=${settings.frameRate}',

      // Video encoding settings
      '-c:v', settings.codec,
      '-b:v', '${settings.bitrate}',
      '-maxrate', '${settings.bitrate}',
      '-bufsize', '${settings.bitrate * 2}',
      '-preset', 'ultrafast',
      '-tune', 'zerolatency',

      // Audio settings (if needed)
      '-f', 'lavfi',
      '-i', 'anullsrc=channel_layout=stereo:sample_rate=44100',
      '-c:a', 'aac',
      '-b:a', '128k',

      // Output settings
      '-f', 'flv',
      rtmpUrl,
    ];

    return args.join(' ');
  }
  
  /// Update status based on FFmpeg session state
  void _updateStreamingStatus() {
    if (_currentSession != null) {
      // Check if session is still running
      _currentSession!.getState().then((state) {
        switch (state) {
          case SessionState.created:
          case SessionState.running:
            _updateStatus(_currentStatus.copyWith(
              state: StreamState.streaming,
              lastUpdate: DateTime.now(),
            ));
            break;
          case SessionState.failed:
            _updateStatus(_currentStatus.copyWith(
              state: StreamState.error,
              error: 'FFmpeg session failed',
              lastUpdate: DateTime.now(),
            ));
            break;
          case SessionState.completed:
            _updateStatus(_currentStatus.copyWith(
              state: StreamState.disconnected,
              lastUpdate: DateTime.now(),
            ));
            break;
        }
      });
    }
  }
  
  /// Start monitoring stream status
  void _startStatusMonitoring() {
    _statusTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      _requestStatusUpdate();
    });
  }
  
  /// Stop monitoring stream status
  void _stopStatusMonitoring() {
    _statusTimer?.cancel();
    _statusTimer = null;
  }
  
  /// Request status update from FFmpeg session
  Future<void> _requestStatusUpdate() async {
    try {
      _updateStreamingStatus();
    } catch (e) {
      _logger.w('Failed to get status update: $e');
    }
  }
  
  /// Update current status and notify listeners
  void _updateStatus(StreamStatus newStatus) {
    _currentStatus = newStatus;
    _statusController.add(newStatus);
  }
  
  /// Dispose resources
  void dispose() {
    _stopStatusMonitoring();
    _statusController.close();
  }
}
