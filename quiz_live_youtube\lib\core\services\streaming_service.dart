import 'dart:async';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';

import '../constants/app_constants.dart';
import '../models/youtube_models.dart';

/// Service for handling screen capture and RTMP streaming
class StreamingService {
  static final Logger _logger = Logger();
  static const MethodChannel _channel = MethodChannel('quiz_live_youtube/streaming');
  
  StreamStatus _currentStatus = const StreamStatus();
  final StreamController<StreamStatus> _statusController = 
      StreamController<StreamStatus>.broadcast();
  
  Timer? _statusTimer;
  bool _isInitialized = false;
  
  /// Stream of streaming status updates
  Stream<StreamStatus> get statusStream => _statusController.stream;
  
  /// Current streaming status
  StreamStatus get currentStatus => _currentStatus;
  
  /// Check if streaming is active
  bool get isStreaming => _currentStatus.state == StreamState.streaming;
  
  /// Initialize the streaming service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Request necessary permissions
      await _requestPermissions();
      
      // Initialize native streaming components
      await _channel.invokeMethod('initialize');
      
      // Set up method call handler for native callbacks
      _channel.setMethodCallHandler(_handleMethodCall);
      
      _isInitialized = true;
      _logger.i('Streaming service initialized');
    } catch (e) {
      _logger.e('Failed to initialize streaming service: $e');
      rethrow;
    }
  }
  
  /// Request necessary permissions for streaming
  Future<void> _requestPermissions() async {
    final permissions = [
      Permission.microphone,
      Permission.camera,
      Permission.storage,
    ];
    
    final statuses = await permissions.request();
    
    for (final permission in permissions) {
      if (statuses[permission] != PermissionStatus.granted) {
        _logger.w('Permission ${permission.toString()} not granted');
      }
    }
    
    // Request screen capture permission (handled by native code)
    final hasScreenPermission = await _channel.invokeMethod('requestScreenPermission');
    if (!hasScreenPermission) {
      throw Exception('Screen capture permission required');
    }
  }
  
  /// Start streaming to YouTube
  Future<void> startStreaming({
    required String rtmpUrl,
    required String streamKey,
    StreamSettings? settings,
  }) async {
    if (!_isInitialized) {
      throw Exception('Streaming service not initialized');
    }
    
    if (isStreaming) {
      throw Exception('Already streaming');
    }
    
    try {
      final streamSettings = settings ?? const StreamSettings();
      final fullRtmpUrl = '$rtmpUrl/$streamKey';
      
      _logger.i('Starting stream to: $fullRtmpUrl');
      
      final result = await _channel.invokeMethod('startStreaming', {
        'rtmpUrl': fullRtmpUrl,
        'width': streamSettings.width,
        'height': streamSettings.height,
        'frameRate': streamSettings.frameRate,
        'bitrate': streamSettings.bitrate,
        'codec': streamSettings.codec,
      });
      
      if (result['success'] == true) {
        _updateStatus(_currentStatus.copyWith(
          state: StreamState.connecting,
          lastUpdate: DateTime.now(),
        ));
        
        // Start status monitoring
        _startStatusMonitoring();
        
        _logger.i('Stream started successfully');
      } else {
        throw Exception(result['error'] ?? 'Failed to start stream');
      }
    } catch (e) {
      _logger.e('Failed to start streaming: $e');
      _updateStatus(_currentStatus.copyWith(
        state: StreamState.error,
        error: e.toString(),
        lastUpdate: DateTime.now(),
      ));
      rethrow;
    }
  }
  
  /// Stop streaming
  Future<void> stopStreaming() async {
    if (!isStreaming && _currentStatus.state != StreamState.connecting) {
      return;
    }
    
    try {
      await _channel.invokeMethod('stopStreaming');
      
      _stopStatusMonitoring();
      
      _updateStatus(_currentStatus.copyWith(
        state: StreamState.disconnected,
        bitrate: 0,
        frameRate: 0,
        droppedFrames: 0,
        error: null,
        lastUpdate: DateTime.now(),
      ));
      
      _logger.i('Stream stopped successfully');
    } catch (e) {
      _logger.e('Failed to stop streaming: $e');
      rethrow;
    }
  }
  
  /// Pause streaming
  Future<void> pauseStreaming() async {
    if (!isStreaming) return;
    
    try {
      await _channel.invokeMethod('pauseStreaming');
      _logger.i('Stream paused');
    } catch (e) {
      _logger.e('Failed to pause streaming: $e');
      rethrow;
    }
  }
  
  /// Resume streaming
  Future<void> resumeStreaming() async {
    try {
      await _channel.invokeMethod('resumeStreaming');
      _logger.i('Stream resumed');
    } catch (e) {
      _logger.e('Failed to resume streaming: $e');
      rethrow;
    }
  }
  
  /// Update stream settings during streaming
  Future<void> updateStreamSettings(StreamSettings settings) async {
    if (!isStreaming) return;
    
    try {
      await _channel.invokeMethod('updateSettings', {
        'width': settings.width,
        'height': settings.height,
        'frameRate': settings.frameRate,
        'bitrate': settings.bitrate,
      });
      
      _logger.i('Stream settings updated');
    } catch (e) {
      _logger.e('Failed to update stream settings: $e');
      rethrow;
    }
  }
  
  /// Handle method calls from native code
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onStatusUpdate':
        _handleStatusUpdate(call.arguments);
        break;
      case 'onError':
        _handleStreamError(call.arguments);
        break;
      case 'onConnectionStateChanged':
        _handleConnectionStateChanged(call.arguments);
        break;
      default:
        _logger.w('Unknown method call: ${call.method}');
    }
  }
  
  /// Handle status updates from native code
  void _handleStatusUpdate(Map<String, dynamic> data) {
    _updateStatus(_currentStatus.copyWith(
      bitrate: data['bitrate']?.toInt() ?? 0,
      frameRate: data['frameRate']?.toDouble() ?? 0.0,
      droppedFrames: data['droppedFrames']?.toInt() ?? 0,
      lastUpdate: DateTime.now(),
    ));
  }
  
  /// Handle stream errors from native code
  void _handleStreamError(Map<String, dynamic> data) {
    final error = data['error'] as String?;
    _logger.e('Stream error: $error');
    
    _updateStatus(_currentStatus.copyWith(
      state: StreamState.error,
      error: error,
      lastUpdate: DateTime.now(),
    ));
  }
  
  /// Handle connection state changes from native code
  void _handleConnectionStateChanged(Map<String, dynamic> data) {
    final stateString = data['state'] as String?;
    StreamState? newState;
    
    switch (stateString) {
      case 'connecting':
        newState = StreamState.connecting;
        break;
      case 'connected':
        newState = StreamState.connected;
        break;
      case 'streaming':
        newState = StreamState.streaming;
        break;
      case 'disconnected':
        newState = StreamState.disconnected;
        break;
      case 'error':
        newState = StreamState.error;
        break;
      case 'reconnecting':
        newState = StreamState.reconnecting;
        break;
    }
    
    if (newState != null) {
      _updateStatus(_currentStatus.copyWith(
        state: newState,
        lastUpdate: DateTime.now(),
      ));
    }
  }
  
  /// Start monitoring stream status
  void _startStatusMonitoring() {
    _statusTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      _requestStatusUpdate();
    });
  }
  
  /// Stop monitoring stream status
  void _stopStatusMonitoring() {
    _statusTimer?.cancel();
    _statusTimer = null;
  }
  
  /// Request status update from native code
  Future<void> _requestStatusUpdate() async {
    try {
      await _channel.invokeMethod('getStatus');
    } catch (e) {
      _logger.w('Failed to get status update: $e');
    }
  }
  
  /// Update current status and notify listeners
  void _updateStatus(StreamStatus newStatus) {
    _currentStatus = newStatus;
    _statusController.add(newStatus);
  }
  
  /// Dispose resources
  void dispose() {
    _stopStatusMonitoring();
    _statusController.close();
  }
}
