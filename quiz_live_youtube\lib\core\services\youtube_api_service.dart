import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';

import '../constants/app_constants.dart';
import '../models/youtube_models.dart';
import 'youtube_auth_service.dart';

/// Service for YouTube API operations
class YouTubeApiService {
  static final Logger _logger = Logger();
  final YouTubeAuthService _authService;
  
  YouTubeApiService(this._authService);
  
  /// Get user's YouTube channel information
  Future<YouTubeChannel> getChannelInfo() async {
    try {
      final response = await http.get(
        Uri.parse('${AppConstants.youtubeApiBaseUrl}/channels?part=snippet,statistics&mine=true'),
        headers: _authService.getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final items = data['items'] as List;

        if (items.isEmpty) {
          throw Exception('No channel found');
        }

        final channel = items.first;
        final snippet = channel['snippet'];
        final statistics = channel['statistics'];

        return YouTubeChannel(
          id: channel['id'],
          title: snippet['title'],
          description: snippet['description'] ?? '',
          thumbnailUrl: snippet['thumbnails']?['default']?['url'],
          subscriberCount: int.tryParse(statistics?['subscriberCount'] ?? '0'),
          videoCount: int.tryParse(statistics?['videoCount'] ?? '0'),
        );
      } else if (response.statusCode == 401) {
        _logger.e('Authentication failed - token may be expired');
        throw Exception('Authentication failed: ${response.statusCode}');
      } else {
        _logger.e('Failed to get channel info: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to get channel info: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Failed to get channel info: $e');
      rethrow;
    }
  }
  
  /// Create a new live broadcast
  Future<YouTubeLiveBroadcast> createLiveBroadcast({
    required String title,
    required String description,
    DateTime? scheduledStartTime,
    String privacyStatus = 'public',
  }) async {
    try {
      final broadcastData = {
        'snippet': {
          'title': title,
          'description': description,
          'scheduledStartTime': (scheduledStartTime ?? DateTime.now()).toIso8601String(),
        },
        'status': {
          'privacyStatus': privacyStatus,
          'selfDeclaredMadeForKids': false,
        },
        'contentDetails': {
          'enableAutoStart': false,
          'enableAutoStop': false,
          'enableDvr': true,
          'enableContentEncryption': false,
          'startWithSlate': false,
          'recordFromStart': true,
        },
      };
      
      final response = await http.post(
        Uri.parse('${AppConstants.youtubeApiBaseUrl}/liveBroadcasts?part=snippet,status,contentDetails'),
        headers: _authService.getAuthHeaders(),
        body: json.encode(broadcastData),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final snippet = data['snippet'];
        final status = data['status'];
        
        return YouTubeLiveBroadcast(
          id: data['id'],
          title: snippet['title'],
          description: snippet['description'],
          status: status['lifeCycleStatus'],
          privacyStatus: status['privacyStatus'],
          scheduledStartTime: DateTime.tryParse(snippet['scheduledStartTime'] ?? ''),
        );
      } else {
        throw Exception('Failed to create broadcast: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Failed to create live broadcast: $e');
      rethrow;
    }
  }
  
  /// Create a live stream
  Future<YouTubeLiveStream> createLiveStream({
    required String title,
    required StreamSettings settings,
  }) async {
    try {
      final streamData = {
        'snippet': {
          'title': title,
        },
        'cdn': {
          'frameRate': '${settings.frameRate}fps',
          'ingestionType': 'rtmp',
          'resolution': '${settings.height}p',
        },
      };
      
      final response = await http.post(
        Uri.parse('${AppConstants.youtubeApiBaseUrl}/liveStreams?part=snippet,cdn,status'),
        headers: _authService.getAuthHeaders(),
        body: json.encode(streamData),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final snippet = data['snippet'];
        final cdn = data['cdn'];
        final status = data['status'];
        
        return YouTubeLiveStream(
          id: data['id'],
          title: snippet['title'],
          status: status['streamStatus'],
          settings: settings,
          ingestionAddress: cdn['ingestionInfo']?['ingestionAddress'],
          streamName: cdn['ingestionInfo']?['streamName'],
        );
      } else {
        throw Exception('Failed to create stream: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Failed to create live stream: $e');
      rethrow;
    }
  }
  
  /// Bind broadcast to stream
  Future<void> bindBroadcastToStream(String broadcastId, String streamId) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConstants.youtubeApiBaseUrl}/liveBroadcasts/bind?id=$broadcastId&streamId=$streamId&part=id,contentDetails'),
        headers: _authService.getAuthHeaders(),
      );
      
      if (response.statusCode != 200) {
        throw Exception('Failed to bind broadcast to stream: ${response.statusCode}');
      }
      
      _logger.i('Successfully bound broadcast to stream');
    } catch (e) {
      _logger.e('Failed to bind broadcast to stream: $e');
      rethrow;
    }
  }
  
  /// Start live broadcast
  Future<void> startLiveBroadcast(String broadcastId) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConstants.youtubeApiBaseUrl}/liveBroadcasts/transition?broadcastStatus=live&id=$broadcastId&part=id,status'),
        headers: _authService.getAuthHeaders(),
      );
      
      if (response.statusCode != 200) {
        throw Exception('Failed to start broadcast: ${response.statusCode}');
      }
      
      _logger.i('Successfully started live broadcast');
    } catch (e) {
      _logger.e('Failed to start live broadcast: $e');
      rethrow;
    }
  }
  
  /// Stop live broadcast
  Future<void> stopLiveBroadcast(String broadcastId) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConstants.youtubeApiBaseUrl}/liveBroadcasts/transition?broadcastStatus=complete&id=$broadcastId&part=id,status'),
        headers: _authService.getAuthHeaders(),
      );
      
      if (response.statusCode != 200) {
        throw Exception('Failed to stop broadcast: ${response.statusCode}');
      }
      
      _logger.i('Successfully stopped live broadcast');
    } catch (e) {
      _logger.e('Failed to stop live broadcast: $e');
      rethrow;
    }
  }
  
  /// Get live chat ID for a broadcast
  Future<String?> getLiveChatId(String broadcastId) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConstants.youtubeApiBaseUrl}/liveBroadcasts?part=snippet&id=$broadcastId'),
        headers: _authService.getAuthHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final items = data['items'] as List;
        
        if (items.isNotEmpty) {
          return items.first['snippet']['liveChatId'];
        }
      }
      
      return null;
    } catch (e) {
      _logger.e('Failed to get live chat ID: $e');
      return null;
    }
  }
}
