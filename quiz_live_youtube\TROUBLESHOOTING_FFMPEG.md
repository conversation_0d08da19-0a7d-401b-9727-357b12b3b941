# Guide de Dépannage FFmpeg Kit

## Problèmes d'Installation

### 1. <PERSON><PERSON>ur "Could not find pubspec.yaml"

**Problème :**
```
Could not find a file named "flutter/ffmpeg_kit_flutter/pubspec.yaml" in https://github.com/mrljdx/ffmpeg-kit.git
```

**Solution :**
Le chemin correct dans le repository est `flutter/flutter`. Utilisez cette configuration :

```yaml
dependencies:
  ffmpeg_kit_flutter:
    git:
      url: https://github.com/mrljdx/ffmpeg-kit.git
      path: flutter/flutter
```

### 2. Problème de Résolution de Dépendances

**Problème :**
```
Failed to update packages.
```

**Solutions :**

1. **Nettoyer le cache Flutter :**
   ```bash
   flutter clean
   flutter pub cache clean
   flutter pub get
   ```

2. **Vérifier la connectivité Git :**
   ```bash
   git ls-remote https://github.com/mrljdx/ffmpeg-kit.git
   ```

3. **Utiliser une version spécifique :**
   ```yaml
   ffmpeg_kit_flutter:
     git:
       url: https://github.com/mrljdx/ffmpeg-kit.git
       path: flutter/flutter
       ref: main  # ou un tag spécifique
   ```

### 3. Alternative avec Package Officiel

Si le repository personnalisé pose problème, utilisez temporairement le package officiel :

```yaml
dependencies:
  ffmpeg_kit_flutter: ^6.0.3
```

## Problèmes de Compilation

### 1. Erreurs d'Import

**Problème :**
```
The imported package 'ffmpeg_kit_flutter' isn't a dependency
```

**Solution :**
1. Vérifiez que `flutter pub get` s'est exécuté sans erreur
2. Redémarrez votre IDE
3. Exécutez `flutter clean && flutter pub get`

### 2. Méthodes Non Trouvées

**Problème :**
```
The method 'enableLogCallback' isn't defined for the type 'FFmpegKit'
```

**Solution :**
Certaines méthodes peuvent différer entre les versions. Utilisez cette approche :

```dart
// Au lieu de
FFmpegKit.enableLogCallback((log) {
  print(log.getMessage());
});

// Utilisez
FFmpegKit.executeAsync(
  command,
  (session) async {
    // Callback de session
  },
  (log) {
    // Callback de log
    print('FFmpeg: ${log.getMessage()}');
  },
  (statistics) {
    // Callback de statistiques
  },
);
```

## Problèmes d'Exécution

### 1. Session FFmpeg Échoue

**Problème :**
```
FFmpeg session failed with return code: -1
```

**Solutions :**

1. **Vérifier la commande :**
   ```dart
   final command = '-f lavfi -i testsrc=size=640x480:rate=30 -t 5 -f null -';
   final session = await FFmpegKit.execute(command);
   final returnCode = await session.getReturnCode();
   
   if (!ReturnCode.isSuccess(returnCode)) {
     final logs = await session.getAllLogs();
     for (final log in logs) {
       print('Error: ${log.getMessage()}');
     }
   }
   ```

2. **Tester avec une commande simple :**
   ```dart
   await FFmpegKit.execute('-version');
   ```

### 2. Permissions Manquantes

**Android :**
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
```

**iOS :**
```xml
<!-- ios/Runner/Info.plist -->
<key>NSMicrophoneUsageDescription</key>
<string>Cette app a besoin du microphone pour l'enregistrement</string>
```

## Tests de Validation

### 1. Test d'Installation

Créez un fichier `test_ffmpeg.dart` :

```dart
import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter/return_code.dart';

void main() async {
  try {
    print('Test FFmpeg Kit...');
    
    final session = await FFmpegKit.execute('-version');
    final returnCode = await session.getReturnCode();
    
    if (ReturnCode.isSuccess(returnCode)) {
      print('✅ FFmpeg Kit fonctionne !');
    } else {
      print('❌ FFmpeg Kit ne fonctionne pas');
    }
  } catch (e) {
    print('❌ Erreur: $e');
  }
}
```

Exécutez avec :
```bash
dart test_ffmpeg.dart
```

### 2. Test de Commande RTMP

```dart
Future<void> testRTMPCommand() async {
  final command = [
    '-f', 'lavfi',
    '-i', 'testsrc=size=640x480:rate=30',
    '-t', '10',
    '-f', 'flv',
    'rtmp://test.com/live/key'
  ].join(' ');
  
  print('Commande de test: $command');
  
  final session = await FFmpegKit.execute(command);
  final returnCode = await session.getReturnCode();
  
  print('Code de retour: $returnCode');
}
```

## Problèmes Spécifiques aux Plateformes

### Android

1. **Erreur de Build :**
   ```
   Execution failed for task ':app:mergeDebugNativeLibs'
   ```
   
   **Solution :** Ajoutez dans `android/app/build.gradle` :
   ```gradle
   android {
     packagingOptions {
       pickFirst '**/libc++_shared.so'
       pickFirst '**/libjsc.so'
     }
   }
   ```

2. **Problème de Taille APK :**
   Utilisez le package `min` au lieu de `full` :
   ```yaml
   ffmpeg_kit_flutter_min: ^6.0.3
   ```

### iOS

1. **Erreur de Linking :**
   ```
   Undefined symbols for architecture arm64
   ```
   
   **Solution :** Nettoyez et rebuilder :
   ```bash
   cd ios
   rm -rf Pods Podfile.lock
   pod install
   cd ..
   flutter clean
   flutter build ios
   ```

2. **Problème de Permissions :**
   Ajoutez toutes les permissions nécessaires dans `Info.plist`

## Alternatives et Solutions de Contournement

### 1. Utilisation du Package Officiel

Si le repository personnalisé pose trop de problèmes :

```yaml
dependencies:
  ffmpeg_kit_flutter: ^6.0.3  # Version officielle
```

### 2. Implémentation Progressive

1. **Phase 1 :** Utilisez le package officiel pour les tests
2. **Phase 2 :** Migrez vers le repository personnalisé une fois stable
3. **Phase 3 :** Implémentez les fonctionnalités spécifiques

### 3. Fork Personnel

Créez votre propre fork du repository :

1. Fork `https://github.com/mrljdx/ffmpeg-kit`
2. Modifiez selon vos besoins
3. Utilisez votre fork dans pubspec.yaml

## Support et Ressources

### Documentation Officielle
- [FFmpeg Kit Wiki](https://github.com/arthenica/ffmpeg-kit/wiki)
- [Flutter Documentation](https://docs.flutter.dev/)

### Communauté
- [GitHub Issues](https://github.com/arthenica/ffmpeg-kit/issues)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/ffmpeg-kit)

### Logs de Debug

Activez les logs détaillés :

```dart
FFmpegKit.enableLogCallback((log) {
  print('FFmpeg [${log.getLevel()}]: ${log.getMessage()}');
});

FFmpegKit.enableStatisticsCallback((statistics) {
  print('Stats: ${statistics.toString()}');
});
```

## Checklist de Dépannage

- [ ] Vérifier la configuration pubspec.yaml
- [ ] Exécuter `flutter clean && flutter pub get`
- [ ] Tester avec une commande simple (`-version`)
- [ ] Vérifier les permissions
- [ ] Consulter les logs FFmpeg
- [ ] Tester sur un appareil physique
- [ ] Vérifier la connectivité réseau (pour Git)
- [ ] Essayer le package officiel en alternative

Si tous ces points sont vérifiés et que le problème persiste, considérez l'utilisation du package officiel `ffmpeg_kit_flutter` en attendant une résolution.
