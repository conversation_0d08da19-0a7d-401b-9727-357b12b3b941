1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.quiz_live_youtube"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:3:5-67
15-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:4:5-71
16-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:4:22-68
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:5:5-65
17-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:5:22-62
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:6:5-81
18-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:6:22-78
19    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:7:5-80
19-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:7:22-77
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
20-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:8:5-77
20-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:8:22-74
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:9:5-68
21-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:9:22-65
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:10:5-79
22-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:10:22-76
23    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- Required for MediaProjection (screen recording) -->
23-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:11:5-76
23-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:11:22-73
24    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- Features -->
24-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:14:5-78
24-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:14:22-75
25    <uses-feature
25-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:17:5-85
26        android:name="android.hardware.camera"
26-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:17:19-57
27        android:required="false" />
27-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:17:58-82
28    <uses-feature
28-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:18:5-95
29        android:name="android.hardware.camera.autofocus"
29-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:18:19-67
30        android:required="false" />
30-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:18:68-92
31    <uses-feature
31-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:19:5-89
32        android:name="android.hardware.microphone"
32-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:19:19-61
33        android:required="false" />
33-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:19:62-86
34    <!--
35 Required to query activities that can process text, see:
36         https://developer.android.com/training/package-visibility and
37         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
38
39         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
40    -->
41    <queries>
41-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:58:5-63:15
42        <intent>
42-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:59:9-62:18
43            <action android:name="android.intent.action.PROCESS_TEXT" />
43-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:60:13-72
43-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:60:21-70
44
45            <data android:mimeType="text/plain" />
45-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:61:13-50
45-->C:\Users\<USER>\Documents\augment-projects\QuizLiveYoutube\quiz_live_youtube\android\app\src\main\AndroidManifest.xml:61:19-48
46        </intent>
47    </queries>
48
49    <permission
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
50        android:name="com.example.quiz_live_youtube.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
51        android:protectionLevel="signature" />
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
52
53    <uses-permission android:name="com.example.quiz_live_youtube.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
54
55    <application
56        android:name="android.app.Application"
57        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
58        android:debuggable="true"
59        android:extractNativeLibs="false"
60        android:icon="@mipmap/ic_launcher"
61        android:label="quiz_live_youtube" >
62        <activity
63            android:name="com.example.quiz_live_youtube.MainActivity"
64            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
65            android:exported="true"
66            android:hardwareAccelerated="true"
67            android:launchMode="singleTop"
68            android:taskAffinity=""
69            android:theme="@style/LaunchTheme"
70            android:windowSoftInputMode="adjustResize" >
71
72            <!--
73                 Specifies an Android theme to apply to this Activity as soon as
74                 the Android process has started. This theme is visible to the user
75                 while the Flutter UI initializes. After that, this theme continues
76                 to determine the Window background behind the Flutter UI.
77            -->
78            <meta-data
79                android:name="io.flutter.embedding.android.NormalTheme"
80                android:resource="@style/NormalTheme" />
81
82            <intent-filter>
83                <action android:name="android.intent.action.MAIN" />
84
85                <category android:name="android.intent.category.LAUNCHER" />
86            </intent-filter>
87        </activity>
88        <!--
89             Don't delete the meta-data below.
90             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
91        -->
92        <meta-data
93            android:name="flutterEmbedding"
94            android:value="2" />
95
96        <activity
96-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
97            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
97-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
98            android:excludeFromRecents="true"
98-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
99            android:exported="false"
99-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
100            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
100-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
101        <!--
102            Service handling Google Sign-In user revocation. For apps that do not integrate with
103            Google Sign-In, this service will never be started.
104        -->
105        <service
105-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
106            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
106-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
107            android:exported="true"
107-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
108            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
108-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
109            android:visibleToInstantApps="true" />
109-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b82c565a22264b65cca6cdec1e8b659\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
110
111        <activity
111-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b6482155e5a178adf635640ae2a82c\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
112            android:name="com.google.android.gms.common.api.GoogleApiActivity"
112-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b6482155e5a178adf635640ae2a82c\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
113            android:exported="false"
113-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b6482155e5a178adf635640ae2a82c\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
114            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
114-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b6482155e5a178adf635640ae2a82c\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
115
116        <meta-data
116-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84cfce9c05a54a984dea12df260c2609\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
117            android:name="com.google.android.gms.version"
117-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84cfce9c05a54a984dea12df260c2609\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
118            android:value="@integer/google_play_services_version" />
118-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84cfce9c05a54a984dea12df260c2609\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
119
120        <provider
120-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
121            android:name="androidx.startup.InitializationProvider"
121-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
122            android:authorities="com.example.quiz_live_youtube.androidx-startup"
122-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
123            android:exported="false" >
123-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
124            <meta-data
124-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.emoji2.text.EmojiCompatInitializer"
125-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
126                android:value="androidx.startup" />
126-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
127            <meta-data
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
129                android:value="androidx.startup" />
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
130            <meta-data
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
132                android:value="androidx.startup" />
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
133        </provider>
134
135        <uses-library
135-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
136            android:name="androidx.window.extensions"
136-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
137            android:required="false" />
137-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
138        <uses-library
138-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
139            android:name="androidx.window.sidecar"
139-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
140            android:required="false" />
140-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
141
142        <receiver
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
143            android:name="androidx.profileinstaller.ProfileInstallReceiver"
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
144            android:directBootAware="false"
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
145            android:enabled="true"
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
146            android:exported="true"
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
147            android:permission="android.permission.DUMP" >
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
148            <intent-filter>
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
149                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
150            </intent-filter>
151            <intent-filter>
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
152                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
153            </intent-filter>
154            <intent-filter>
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
155                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
156            </intent-filter>
157            <intent-filter>
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
158                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
159            </intent-filter>
160        </receiver>
161    </application>
162
163</manifest>
