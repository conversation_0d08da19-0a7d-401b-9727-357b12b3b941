import 'package:flutter/material.dart';

import '../../presentation/screens/splash_screen.dart';
import '../../presentation/screens/auth_screen.dart';
import '../../presentation/screens/home_screen.dart';
import '../../presentation/screens/quiz_management_screen.dart';
import '../../presentation/screens/live_quiz_screen.dart';
import '../../presentation/screens/streaming_setup_screen.dart';

/// Navigation utility class for managing app routes
class NavigationUtils {
  // Route names
  static const String splash = '/';
  static const String auth = '/auth';
  static const String home = '/home';
  static const String quizManagement = '/quiz-management';
  static const String liveQuiz = '/live-quiz';
  static const String streamingSetup = '/streaming-setup';

  /// Generate routes for the app
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return MaterialPageRoute(
          builder: (_) => const SplashScreen(),
          settings: settings,
        );
      case auth:
        return MaterialPageRoute(
          builder: (_) => const AuthScreen(),
          settings: settings,
        );
      case home:
        return MaterialPageRoute(
          builder: (_) => const HomeScreen(),
          settings: settings,
        );
      case quizManagement:
        return MaterialPageRoute(
          builder: (_) => const QuizManagementScreen(),
          settings: settings,
        );
      case liveQuiz:
        return MaterialPageRoute(
          builder: (_) => const LiveQuizScreen(),
          settings: settings,
        );
      case streamingSetup:
        return MaterialPageRoute(
          builder: (_) => const StreamingSetupScreen(),
          settings: settings,
        );
      default:
        return MaterialPageRoute(
          builder: (_) => const NotFoundScreen(),
          settings: settings,
        );
    }
  }

  /// Navigate to auth screen and clear stack
  static void navigateToAuth(BuildContext context) {
    Navigator.of(context).pushNamedAndRemoveUntil(
      auth,
      (route) => false,
    );
  }

  /// Navigate to home screen and clear stack
  static void navigateToHome(BuildContext context) {
    Navigator.of(context).pushNamedAndRemoveUntil(
      home,
      (route) => false,
    );
  }

  /// Navigate to quiz management
  static void navigateToQuizManagement(BuildContext context) {
    Navigator.of(context).pushNamed(quizManagement);
  }

  /// Navigate to live quiz
  static void navigateToLiveQuiz(BuildContext context) {
    Navigator.of(context).pushNamed(liveQuiz);
  }

  /// Navigate to streaming setup
  static void navigateToStreamingSetup(BuildContext context) {
    Navigator.of(context).pushNamed(streamingSetup);
  }

  /// Show error dialog
  static void showErrorDialog(
    BuildContext context, {
    required String title,
    required String message,
    VoidCallback? onOk,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onOk?.call();
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show confirmation dialog
  static Future<bool> showConfirmationDialog(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// Show snackbar
  static void showSnackBar(
    BuildContext context, {
    required String message,
    bool isError = false,
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError 
            ? Theme.of(context).colorScheme.error 
            : null,
        duration: duration,
      ),
    );
  }
}

/// 404 Not Found screen
class NotFoundScreen extends StatelessWidget {
  const NotFoundScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Page Not Found'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '404',
              style: TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Page not found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
