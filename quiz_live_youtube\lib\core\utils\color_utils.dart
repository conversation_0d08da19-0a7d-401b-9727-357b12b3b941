import 'package:flutter/material.dart';

/// Utility class for color operations
class ColorUtils {
  /// Creates a color with the specified opacity
  /// This is a replacement for the deprecated withOpacity method
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }
  
  /// Creates a semi-transparent version of a color (50% opacity)
  static Color semiTransparent(Color color) {
    return withOpacity(color, 0.5);
  }
  
  /// Creates a subtle version of a color (70% opacity)
  static Color subtle(Color color) {
    return withOpacity(color, 0.7);
  }
  
  /// Creates a faded version of a color (30% opacity)
  static Color faded(Color color) {
    return withOpacity(color, 0.3);
  }
  
  /// Creates a very faded version of a color (10% opacity)
  static Color veryFaded(Color color) {
    return withOpacity(color, 0.1);
  }
}
