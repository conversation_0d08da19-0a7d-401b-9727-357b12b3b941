import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/models/quiz_models.dart';
import '../../core/providers/quiz_providers.dart';
import '../../core/providers/streaming_providers.dart';

/// Screen for conducting live quiz sessions
class LiveQuizScreen extends ConsumerWidget {
  const LiveQuizScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentSession = ref.watch(liveQuizSessionProvider);
    final isStreaming = ref.watch(isStreamingProvider);
    
    if (currentSession == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Live Quiz'),
        ),
        body: const Center(
          child: Text('No active quiz session'),
        ),
      );
    }
    
    return Scaffold(
      appBar: AppBar(
        title: Text(currentSession.quiz.title),
        actions: [
          if (isStreaming)
            Container(
              margin: const EdgeInsets.all(8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.circle, color: Colors.white, size: 8),
                  SizedBox(width: 4),
                  Text('LIVE', style: TextStyle(color: Colors.white, fontSize: 12)),
                ],
              ),
            ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(context, ref, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'stop',
                child: ListTile(
                  leading: Icon(Icons.stop),
                  title: Text('Stop Session'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Session Status
          _buildSessionStatus(context, currentSession),
          
          // Current Question or Results
          Expanded(
            child: _buildMainContent(context, ref, currentSession),
          ),
          
          // Controls
          _buildControls(context, ref, currentSession),
        ],
      ),
    );
  }

  Widget _buildSessionStatus(BuildContext context, dynamic currentSession) {
    final theme = Theme.of(context);
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      color: theme.colorScheme.primaryContainer,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Question ${currentSession.currentQuestionIndex + 1} of ${currentSession.quiz.questions.length}',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onPrimaryContainer,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Status: ${currentSession.status.toString().split('.').last.toUpperCase()}',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onPrimaryContainer,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent(BuildContext context, WidgetRef ref, dynamic currentSession) {
    // TODO: Implement based on session status
    switch (currentSession.status) {
      case QuizSessionStatus.waiting:
        return _buildWaitingContent(context);
      case QuizSessionStatus.questionActive:
        return _buildActiveQuestionContent(context, currentSession);
      case QuizSessionStatus.questionCompleted:
        return _buildQuestionResultsContent(context, currentSession);
      case QuizSessionStatus.completed:
        return _buildSessionCompletedContent(context, currentSession);
      case QuizSessionStatus.paused:
        return _buildWaitingContent(context);
      case QuizSessionStatus.active:
        return _buildWaitingContent(context);
    }
  }

  Widget _buildWaitingContent(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.play_circle_outline,
            size: 96,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: 24),
          Text(
            'Ready to Start',
            style: theme.textTheme.headlineSmall,
          ),
          const SizedBox(height: 12),
          Text(
            'Press "Start Question" to begin the quiz',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveQuestionContent(BuildContext context, dynamic currentSession) {
    final theme = Theme.of(context);
    final currentQuestion = currentSession.quiz.questions[currentSession.currentQuestionIndex];
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    currentQuestion.title,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    currentQuestion.questionText,
                    style: theme.textTheme.bodyLarge,
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Answer Options
          Text(
            'Answer Options:',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          
          ...currentQuestion.options.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final label = String.fromCharCode(65 + index.toInt()); // A, B, C, D
            
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: theme.colorScheme.primary,
                  child: Text(
                    label,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                title: Text(option),
                trailing: index == currentQuestion.correctAnswerIndex
                    ? Icon(
                        Icons.check_circle,
                        color: Colors.green,
                      )
                    : null,
              ),
            );
          }),
          
          const Spacer(),
          
          // Timer (placeholder)
          Card(
            color: theme.colorScheme.errorContainer,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    Icons.timer,
                    color: theme.colorScheme.onErrorContainer,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Time remaining: ${currentQuestion.timeLimit}s',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onErrorContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionResultsContent(BuildContext context, dynamic currentSession) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            size: 96,
            color: Colors.green,
          ),
          const SizedBox(height: 24),
          Text(
            'Question Completed!',
            style: theme.textTheme.headlineSmall,
          ),
          const SizedBox(height: 12),
          Text(
            'Results are being processed...',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionCompletedContent(BuildContext context, dynamic currentSession) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.celebration,
            size: 96,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: 24),
          Text(
            'Quiz Completed!',
            style: theme.textTheme.headlineSmall,
          ),
          const SizedBox(height: 12),
          Text(
            'Thank you for participating!',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControls(BuildContext context, WidgetRef ref, dynamic currentSession) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (currentSession.status == QuizSessionStatus.waiting ||
              currentSession.status == QuizSessionStatus.questionCompleted) ...[
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _startNextQuestion(ref),
                icon: const Icon(Icons.play_arrow),
                label: Text(
                  currentSession.currentQuestionIndex < currentSession.quiz.questions.length
                      ? 'Start Question'
                      : 'Complete Quiz',
                ),
              ),
            ),
          ],
          
          if (currentSession.status == QuizSessionStatus.questionActive) ...[
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _skipQuestion(ref),
                icon: const Icon(Icons.skip_next),
                label: const Text('Skip Question'),
              ),
            ),
          ],
          
          const SizedBox(width: 12),
          
          ElevatedButton.icon(
            onPressed: () => _stopSession(context, ref),
            icon: const Icon(Icons.stop),
            label: const Text('Stop'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _startNextQuestion(WidgetRef ref) async {
    try {
      await ref.read(liveQuizSessionProvider.notifier).startNextQuestion();
    } catch (e) {
      // Handle error
    }
  }

  void _skipQuestion(WidgetRef ref) async {
    try {
      await ref.read(liveQuizSessionProvider.notifier).skipCurrentQuestion();
    } catch (e) {
      // Handle error
    }
  }

  void _stopSession(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Stop Quiz Session'),
        content: const Text('Are you sure you want to stop the current quiz session?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(liveQuizSessionProvider.notifier).stopSession();
                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              } catch (e) {
                // Handle error
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Stop'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(BuildContext context, WidgetRef ref, String action) {
    switch (action) {
      case 'stop':
        _stopSession(context, ref);
        break;
    }
  }
}
