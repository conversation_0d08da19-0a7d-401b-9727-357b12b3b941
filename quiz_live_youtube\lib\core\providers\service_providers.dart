import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../services/database_service.dart';
import '../services/youtube_auth_service.dart';
import '../services/youtube_api_service.dart';
import '../services/youtube_chat_service.dart';
import '../services/streaming_service.dart';
import '../services/quiz_service.dart';

/// Provider for database service
final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService();
});

/// Provider for YouTube authentication service
final youtubeAuthServiceProvider = Provider<YouTubeAuthService>((ref) {
  return YouTubeAuthService();
});

/// Provider for YouTube API service
final youtubeApiServiceProvider = Provider<YouTubeApiService>((ref) {
  final authService = ref.watch(youtubeAuthServiceProvider);
  return YouTubeApiService(authService);
});

/// Provider for YouTube chat service
final youtubeChatServiceProvider = Provider<YouTubeChatService>((ref) {
  final authService = ref.watch(youtubeAuthServiceProvider);
  return YouTubeChatService(authService);
});

/// Provider for streaming service
final streamingServiceProvider = Provider<StreamingService>((ref) {
  return StreamingService();
});

/// Provider for quiz service
final quizServiceProvider = Provider<QuizService>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  final chatService = ref.watch(youtubeChatServiceProvider);
  return QuizService(databaseService, chatService);
});

/// Provider for initialization status
final initializationProvider = FutureProvider<bool>((ref) async {
  try {
    // Initialize all services
    final authService = ref.read(youtubeAuthServiceProvider);
    final streamingService = ref.read(streamingServiceProvider);
    
    await Future.wait([
      authService.initialize(),
      streamingService.initialize(),
    ]);
    
    return true;
  } catch (e) {
    throw Exception('Failed to initialize services: $e');
  }
});
