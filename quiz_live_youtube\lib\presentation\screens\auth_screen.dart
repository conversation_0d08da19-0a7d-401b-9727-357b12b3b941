import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/constants/app_constants.dart';
import '../../core/providers/auth_providers.dart';
import 'home_screen.dart';

/// Authentication screen for YouTube sign-in
class AuthScreen extends ConsumerStatefulWidget {
  const AuthScreen({super.key});

  @override
  ConsumerState<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends ConsumerState<AuthScreen> {
  bool _isSigningIn = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authStatus = ref.watch(youtubeAuthStatusProvider);
    
    // Listen to auth status changes
    ref.listen<AsyncValue<dynamic>>(youtubeAuthStatusProvider, (previous, next) {
      next.when(
        data: (token) {
          if (token != null && mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => const HomeScreen(),
              ),
            );
          }
        },
        loading: () {},
        error: (error, _) {
          if (mounted) {
            _showErrorDialog(error.toString());
          }
        },
      );
    });

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              theme.colorScheme.primary,
              theme.colorScheme.primary.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                const Spacer(),
                
                // App Logo and Title
                _buildHeader(theme),
                
                const SizedBox(height: 48),
                
                // Features List
                _buildFeaturesList(theme),
                
                const Spacer(),
                
                // Sign In Section
                _buildSignInSection(theme, authStatus),
                
                const SizedBox(height: 24),
                
                // Terms and Privacy
                _buildTermsAndPrivacy(theme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Column(
      children: [
        // App Icon
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: const Icon(
            Icons.quiz,
            size: 50,
            color: Color(0xFF1976D2),
          ),
        ).animate()
          .scale(duration: 600.ms, curve: Curves.elasticOut)
          .then(delay: 200.ms)
          .shimmer(duration: 1000.ms),
        
        const SizedBox(height: 24),
        
        // App Name
        Text(
          AppConstants.appName,
          style: theme.textTheme.displaySmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ).animate()
          .fadeIn(delay: 400.ms, duration: 600.ms)
          .slideY(begin: 0.3, end: 0),
        
        const SizedBox(height: 12),
        
        // App Description
        Text(
          'Create engaging live quizzes for your YouTube audience',
          style: theme.textTheme.titleMedium?.copyWith(
            color: Colors.white.withOpacity(0.9),
            fontWeight: FontWeight.w400,
          ),
          textAlign: TextAlign.center,
        ).animate()
          .fadeIn(delay: 600.ms, duration: 600.ms)
          .slideY(begin: 0.3, end: 0),
      ],
    );
  }

  Widget _buildFeaturesList(ThemeData theme) {
    final features = [
      {
        'icon': Icons.live_tv,
        'title': 'Live Streaming',
        'description': 'Stream directly to YouTube Live',
      },
      {
        'icon': Icons.quiz,
        'title': 'Interactive Quizzes',
        'description': 'Create engaging multiple-choice questions',
      },
      {
        'icon': Icons.chat,
        'title': 'Real-time Chat',
        'description': 'Monitor YouTube chat for answers',
      },
      {
        'icon': Icons.leaderboard,
        'title': 'Live Results',
        'description': 'Display winners and leaderboards',
      },
    ];

    return Card(
      color: Colors.white.withOpacity(0.1),
      elevation: 0,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Features',
              style: theme.textTheme.titleLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...features.asMap().entries.map((entry) {
              final index = entry.key;
              final feature = entry.value;
              
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        feature['icon'] as IconData,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            feature['title'] as String,
                            style: theme.textTheme.titleSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            feature['description'] as String,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.white.withOpacity(0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ).animate()
                .fadeIn(delay: (800 + index * 100).ms, duration: 400.ms)
                .slideX(begin: -0.3, end: 0);
            }),
          ],
        ),
      ),
    ).animate()
      .fadeIn(delay: 800.ms, duration: 600.ms)
      .slideY(begin: 0.3, end: 0);
  }

  Widget _buildSignInSection(ThemeData theme, AsyncValue<dynamic> authStatus) {
    return Column(
      children: [
        Text(
          'Sign in with your YouTube account to get started',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: Colors.white.withOpacity(0.9),
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 24),
        
        // Sign In Button
        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton.icon(
            onPressed: _isSigningIn ? null : _signInWithYouTube,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: theme.colorScheme.primary,
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: _isSigningIn
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.login, size: 20),
            label: Text(
              _isSigningIn ? 'Signing in...' : 'Sign in with Google',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ).animate()
          .fadeIn(delay: 1200.ms, duration: 400.ms)
          .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0)),
      ],
    );
  }

  Widget _buildTermsAndPrivacy(ThemeData theme) {
    return Text(
      'By signing in, you agree to our Terms of Service and Privacy Policy',
      style: theme.textTheme.bodySmall?.copyWith(
        color: Colors.white.withOpacity(0.7),
      ),
      textAlign: TextAlign.center,
    ).animate()
      .fadeIn(delay: 1400.ms, duration: 400.ms);
  }

  Future<void> _signInWithYouTube() async {
    setState(() {
      _isSigningIn = true;
    });

    try {
      await ref.read(youtubeAuthStatusProvider.notifier).signIn();
    } catch (e) {
      if (mounted) {
        _showErrorDialog(e.toString());
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSigningIn = false;
        });
      }
    }
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign In Error'),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
