import 'dart:convert';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';

import '../constants/app_constants.dart';
import '../config/app_config.dart';
import '../models/youtube_models.dart';

/// Service for handling YouTube authentication
class YouTubeAuthService {
  static final Logger _logger = Logger();
  static const String _clientId = AppConfig.youtubeClientId;
  
  late final GoogleSignIn _googleSignIn;
  YouTubeToken? _currentToken;
  
  YouTubeAuthService() {
    _googleSignIn = GoogleSignIn(
      clientId: _clientId,
      scopes: AppConstants.youtubeScopes,
    );
  }
  
  /// Get current authentication token
  YouTubeToken? get currentToken => _currentToken;
  
  /// Check if user is currently authenticated
  bool get isAuthenticated => _currentToken != null && !_isTokenExpired();
  
  /// Initialize the service and load saved token
  Future<void> initialize() async {
    try {
      // Validate configuration
      if (!AppConfig.isConfigurationValid) {
        _logger.w('YouTube client ID not configured properly');
        throw Exception('YouTube client ID not configured. Please set YOUTUBE_CLIENT_ID environment variable.');
      }

      await _loadSavedToken();
      if (_currentToken != null && _isTokenExpired()) {
        await _refreshToken();
      }
    } catch (e) {
      _logger.e('Failed to initialize YouTube auth service: $e');
      rethrow;
    }
  }
  
  /// Sign in with Google and get YouTube access
  Future<YouTubeToken?> signIn() async {
    try {
      final GoogleSignInAccount? account = await _googleSignIn.signIn();
      if (account == null) {
        _logger.w('User cancelled sign in');
        return null;
      }
      
      final GoogleSignInAuthentication auth = await account.authentication;
      
      if (auth.accessToken == null) {
        throw Exception('Failed to get access token');
      }
      
      _currentToken = YouTubeToken(
        accessToken: auth.accessToken!,
        refreshToken: auth.idToken ?? '', // Use idToken as refresh token fallback
        tokenType: 'Bearer',
        expiresIn: 3600, // Default 1 hour
        scopes: AppConstants.youtubeScopes,
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );
      
      await _saveToken(_currentToken!);
      _logger.i('Successfully signed in to YouTube');
      
      return _currentToken;
    } catch (e) {
      _logger.e('Failed to sign in: $e');
      throw Exception('YouTube sign in failed: $e');
    }
  }
  
  /// Sign out and clear tokens
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _clearSavedToken();
      _currentToken = null;
      _logger.i('Successfully signed out');
    } catch (e) {
      _logger.e('Failed to sign out: $e');
    }
  }
  
  /// Refresh the access token
  Future<void> _refreshToken() async {
    if (_currentToken?.refreshToken == null || _currentToken!.refreshToken.isEmpty) {
      _logger.w('No refresh token available, forcing re-authentication');
      await signOut();
      throw Exception('No refresh token available');
    }

    try {
      final response = await http.post(
        Uri.parse('https://oauth2.googleapis.com/token'),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'client_id': _clientId,
          'refresh_token': _currentToken!.refreshToken,
          'grant_type': 'refresh_token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _currentToken = _currentToken!.copyWith(
          accessToken: data['access_token'],
          expiresIn: data['expires_in'] ?? 3600,
          expiresAt: DateTime.now().add(Duration(seconds: data['expires_in'] ?? 3600)),
        );

        await _saveToken(_currentToken!);
        _logger.i('Token refreshed successfully');
      } else {
        _logger.e('Failed to refresh token: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to refresh token: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Failed to refresh token: $e');
      await signOut(); // Force re-authentication
      throw Exception('Token refresh failed: $e');
    }
  }
  
  /// Check if current token is expired
  bool _isTokenExpired() {
    if (_currentToken?.expiresAt == null) return true;
    return DateTime.now().isAfter(_currentToken!.expiresAt!);
  }
  
  /// Load saved token from storage
  Future<void> _loadSavedToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tokenJson = prefs.getString(AppConstants.keyYoutubeToken);
      
      if (tokenJson != null) {
        final tokenData = json.decode(tokenJson);
        _currentToken = YouTubeToken.fromJson(tokenData);
        _logger.i('Loaded saved YouTube token');
      }
    } catch (e) {
      _logger.e('Failed to load saved token: $e');
    }
  }
  
  /// Save token to storage
  Future<void> _saveToken(YouTubeToken token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        AppConstants.keyYoutubeToken,
        json.encode(token.toJson()),
      );
    } catch (e) {
      _logger.e('Failed to save token: $e');
    }
  }
  
  /// Clear saved token from storage
  Future<void> _clearSavedToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.keyYoutubeToken);
    } catch (e) {
      _logger.e('Failed to clear saved token: $e');
    }
  }
  
  /// Get authorization headers for API requests
  Map<String, String> getAuthHeaders() {
    if (!isAuthenticated) {
      throw Exception('Not authenticated');
    }
    
    return {
      'Authorization': '${_currentToken!.tokenType} ${_currentToken!.accessToken}',
      'Content-Type': 'application/json',
    };
  }
}
