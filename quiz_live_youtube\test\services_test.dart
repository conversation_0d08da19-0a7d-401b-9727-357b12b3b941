import 'package:flutter_test/flutter_test.dart';
import 'package:quiz_live_youtube/core/services/database_service.dart';
import 'package:quiz_live_youtube/core/services/youtube_auth_service.dart';
import 'package:quiz_live_youtube/core/services/youtube_api_service.dart';
import 'package:quiz_live_youtube/core/services/youtube_chat_service.dart';
import 'package:quiz_live_youtube/core/services/streaming_service.dart';
import 'package:quiz_live_youtube/core/services/quiz_service.dart';
import 'package:quiz_live_youtube/core/config/app_config.dart';

void main() {
  group('Services Instantiation Tests', () {
    test('DatabaseService can be instantiated', () {
      expect(() => DatabaseService(), returnsNormally);
    });

    test('YouTubeAuthService can be instantiated', () {
      expect(() => YouTubeAuthService(), returnsNormally);
    });

    test('StreamingService can be instantiated', () {
      expect(() => StreamingService(), returnsNormally);
    });

    test('YouTubeApiService can be instantiated with auth service', () {
      final authService = YouTubeAuthService();
      expect(() => YouTubeApiService(authService), returnsNormally);
    });

    test('YouTubeChatService can be instantiated with auth service', () {
      final authService = YouTubeAuthService();
      expect(() => YouTubeChatService(authService), returnsNormally);
    });

    test('QuizService can be instantiated with dependencies', () {
      final databaseService = DatabaseService();
      final authService = YouTubeAuthService();
      final chatService = YouTubeChatService(authService);
      
      expect(() => QuizService(databaseService, chatService), returnsNormally);
    });
  });

  group('Configuration Tests', () {
    test('AppConfig provides default values', () {
      expect(AppConfig.youtubeClientId, isNotEmpty);
      expect(AppConfig.isDebugMode, isA<bool>());
      expect(AppConfig.enableLogging, isA<bool>());
      expect(AppConfig.defaultStreamBitrate, isA<int>());
      expect(AppConfig.chatPollIntervalMs, isA<int>());
    });

    test('AppConfig validation works', () {
      expect(AppConfig.isConfigurationValid, isA<bool>());
    });

    test('AppConfig provides configuration summary', () {
      final summary = AppConfig.getConfigSummary();
      expect(summary, isA<Map<String, dynamic>>());
      expect(summary.containsKey('youtubeClientIdSet'), isTrue);
      expect(summary.containsKey('isDebugMode'), isTrue);
      expect(summary.containsKey('enableLogging'), isTrue);
    });
  });

  group('Service Dependencies', () {
    late DatabaseService databaseService;
    late YouTubeAuthService authService;
    late YouTubeChatService chatService;
    late YouTubeApiService apiService;
    late StreamingService streamingService;
    late QuizService quizService;

    setUp(() {
      databaseService = DatabaseService();
      authService = YouTubeAuthService();
      chatService = YouTubeChatService(authService);
      apiService = YouTubeApiService(authService);
      streamingService = StreamingService();
      quizService = QuizService(databaseService, chatService);
    });

    test('All services are properly instantiated', () {
      expect(databaseService, isNotNull);
      expect(authService, isNotNull);
      expect(chatService, isNotNull);
      expect(apiService, isNotNull);
      expect(streamingService, isNotNull);
      expect(quizService, isNotNull);
    });

    test('Services have correct types', () {
      expect(databaseService, isA<DatabaseService>());
      expect(authService, isA<YouTubeAuthService>());
      expect(chatService, isA<YouTubeChatService>());
      expect(apiService, isA<YouTubeApiService>());
      expect(streamingService, isA<StreamingService>());
      expect(quizService, isA<QuizService>());
    });
  });
}
