# Quiz Live YouTube

A Flutter mobile application that enables direct streaming to YouTube Live with interactive quiz functionality and real-time chat integration.

## 🎯 Features

- **📡 Direct YouTube Live Streaming**: Stream your quiz interface directly to YouTube Live
- **🧠 Interactive Quiz System**: Create and manage multiple-choice quizzes with customizable timers
- **💬 Real-time Chat Integration**: Monitor YouTube Live chat and detect correct answers automatically
- **🏆 Live Winner Display**: Show winners and leaderboards in real-time during streams
- **🎨 Modern UI**: Clean Material Design interface with dark/light theme support
- **📱 Android Support**: Optimized for Android devices with screen capture capabilities

## 🛠️ Technical Stack

- **Flutter (Dart)** - Cross-platform UI framework
- **Riverpod** - State management
- **FFmpeg Kit** - Video encoding and RTMP streaming
- **YouTube Data API v3** - Chat reading and live stream management
- **SQLite** - Local data storage
- **Android MediaProjection API** - Screen capture functionality

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- [Flutter SDK](https://flutter.dev/docs/get-started/install) (3.8.0 or higher)
- [Android Studio](https://developer.android.com/studio) with Android SDK
- [Git](https://git-scm.com/)

### YouTube API Setup

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the YouTube Data API v3
4. Create credentials (OAuth 2.0 Client ID) for Android
5. Download the `google-services.json` file
6. Add your OAuth client ID to the app configuration

## 🚀 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd quiz_live_youtube
   ```

2. **Install Flutter dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code files**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Configure YouTube API**
   - Replace `YOUR_YOUTUBE_CLIENT_ID` in `lib/core/services/youtube_auth_service.dart`
   - Add your OAuth client ID from Google Cloud Console

5. **Build and run**
   ```bash
   flutter run
   ```

## ⚙️ Configuration

### YouTube API Configuration

Update the following files with your YouTube API credentials:

**lib/core/services/youtube_auth_service.dart**
```dart
static const String _clientId = 'YOUR_YOUTUBE_CLIENT_ID_HERE';
```

### Stream Settings

Default streaming settings can be modified in:
**lib/core/constants/app_constants.dart**
```dart
static const int defaultStreamWidth = 1280;
static const int defaultStreamHeight = 720;
static const int defaultBitrate = 2500000; // 2.5 Mbps
static const int defaultFrameRate = 30;
```

## 📱 Usage

### 1. Authentication
- Launch the app and sign in with your YouTube account
- Grant necessary permissions for streaming and chat access

### 2. Create a Quiz
- Navigate to "Quiz Management"
- Create questions with multiple-choice answers
- Set time limits and correct answers
- Save your quiz for later use

### 3. Setup Streaming
- Go to "Streaming Setup"
- Configure your stream title and description
- Adjust quality settings (resolution, bitrate, frame rate)
- Setup the YouTube Live broadcast

### 4. Start Live Quiz Session
- Select a quiz to start a live session
- Begin streaming to YouTube Live
- Start questions one by one
- Monitor chat for answers in real-time
- Display winners and results live

### 5. Monitor Results
- View real-time leaderboards
- See fastest correct answers
- Track participant engagement

## 🏗️ Project Structure

```
lib/
├── core/
│   ├── constants/          # App-wide constants
│   ├── models/            # Data models (Quiz, YouTube, etc.)
│   ├── providers/         # Riverpod state providers
│   ├── services/          # Business logic services
│   └── utils/             # Utility functions
├── presentation/
│   ├── screens/           # UI screens
│   ├── widgets/           # Reusable UI components
│   └── theme/             # App theming
└── main.dart              # App entry point

android/
├── app/src/main/kotlin/   # Native Android code
│   └── StreamingService.kt # Screen capture & streaming
└── app/build.gradle.kts   # Android dependencies
```

## 🔧 Key Components

### Services
- **YouTubeAuthService**: Handles OAuth authentication
- **YouTubeApiService**: Manages YouTube API calls
- **YouTubeChatService**: Real-time chat monitoring
- **StreamingService**: Screen capture and RTMP streaming
- **QuizService**: Quiz management and session control
- **DatabaseService**: Local data persistence

### State Management
- **Riverpod providers** for reactive state management
- **AsyncValue** for handling loading/error states
- **StateNotifier** for complex state logic

### Native Integration
- **Android MediaProjection** for screen capture
- **FFmpeg Kit** for video encoding and RTMP streaming
- **Method channels** for Flutter-Android communication

## 🚨 Permissions

The app requires the following Android permissions:

- `INTERNET` - Network access for streaming
- `RECORD_AUDIO` - Audio recording (optional)
- `CAMERA` - Camera access (optional)
- `WRITE_EXTERNAL_STORAGE` - File storage
- `FOREGROUND_SERVICE` - Background streaming
- `SYSTEM_ALERT_WINDOW` - Screen capture overlay

## 🐛 Troubleshooting

### Common Issues

**1. YouTube Authentication Failed**
- Verify your OAuth client ID is correct
- Check that YouTube Data API v3 is enabled
- Ensure your app's package name matches the one in Google Cloud Console

**2. Streaming Not Starting**
- Grant screen capture permission when prompted
- Check internet connection
- Verify YouTube Live streaming is enabled on your channel
- Ensure stream key is valid

**3. Chat Not Working**
- Verify YouTube Live chat is enabled
- Check API quotas in Google Cloud Console
- Ensure proper authentication scopes

**4. Build Errors**
- Run `flutter clean && flutter pub get`
- Check Android SDK and build tools versions
- Verify FFmpeg Kit dependency is properly installed

### Debug Mode

Enable debug logging by setting:
```dart
Logger.level = Level.debug;
```

## 📄 API Documentation

### YouTube Data API v3 Endpoints Used

- `GET /channels` - Get channel information
- `POST /liveBroadcasts` - Create live broadcast
- `POST /liveStreams` - Create live stream
- `POST /liveBroadcasts/bind` - Bind stream to broadcast
- `GET /liveChat/messages` - Read chat messages
- `POST /liveChat/messages` - Send chat messages

### Rate Limits

- YouTube Data API: 10,000 units per day (default)
- Chat API: 1 request per 2 seconds (recommended)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Flutter Team](https://flutter.dev/) for the amazing framework
- [FFmpeg Kit](https://github.com/arthenica/ffmpeg-kit) for video processing
- [YouTube Data API](https://developers.google.com/youtube/v3) for live streaming integration
- [Riverpod](https://riverpod.dev/) for state management

## 📞 Support

For support and questions:
- Create an issue in this repository
- Check the [Flutter documentation](https://flutter.dev/docs)
- Review [YouTube API documentation](https://developers.google.com/youtube/v3)

---

**Note**: This app is designed for educational and demonstration purposes. Ensure you comply with YouTube's Terms of Service and Community Guidelines when using live streaming features.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
