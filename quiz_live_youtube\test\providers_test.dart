import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:quiz_live_youtube/core/providers/service_providers.dart';
import 'package:quiz_live_youtube/core/providers/auth_providers.dart';
import 'package:quiz_live_youtube/core/providers/quiz_providers.dart';
import 'package:quiz_live_youtube/core/providers/streaming_providers.dart';
import 'package:quiz_live_youtube/core/models/youtube_models.dart';

void main() {
  group('Provider Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('Service providers can be created', () {
      // Test that all service providers can be instantiated
      expect(() => container.read(databaseServiceProvider), returnsNormally);
      expect(() => container.read(youtubeAuthServiceProvider), returnsNormally);
      expect(() => container.read(youtubeApiServiceProvider), returnsNormally);
      expect(() => container.read(youtubeChatServiceProvider), returnsNormally);
      expect(() => container.read(streamingServiceProvider), returnsNormally);
      expect(() => container.read(quizServiceProvider), returnsNormally);
    });

    test('Auth providers have correct initial state', () {
      // Test auth provider initial state
      final authStatus = container.read(youtubeAuthStatusProvider);
      expect(authStatus, isA<AsyncValue>());
      
      final isAuthenticated = container.read(isAuthenticatedProvider);
      expect(isAuthenticated, isFalse);
    });

    test('Quiz providers have correct initial state', () {
      // Test quiz provider initial state
      final quizzes = container.read(quizzesProvider);
      expect(quizzes, isA<AsyncValue>());
      
      final currentSession = container.read(liveQuizSessionProvider);
      expect(currentSession, isNull);
    });

    test('Streaming providers have correct initial state', () {
      // Test streaming provider initial state
      final currentStatus = container.read(currentStreamStatusProvider);
      expect(currentStatus, isA<StreamStatus>());
      expect(currentStatus.state, StreamState.disconnected);
      
      final isStreaming = container.read(isStreamingProvider);
      expect(isStreaming, isFalse);
      
      final liveBroadcast = container.read(liveBroadcastProvider);
      expect(liveBroadcast, isA<AsyncValue>());
      
      final liveStream = container.read(liveStreamProvider);
      expect(liveStream, isA<AsyncValue>());
      
      final streamingController = container.read(streamingControllerProvider);
      expect(streamingController, isA<StreamingControllerState>());
      expect(streamingController.isSetupComplete, isFalse);
    });

    test('StreamingControllerState copyWith works correctly', () {
      const initialState = StreamingControllerState();
      
      // Test normal copyWith
      final updatedState = initialState.copyWith(
        isSetupComplete: true,
        error: 'Test error',
      );
      
      expect(updatedState.isSetupComplete, isTrue);
      expect(updatedState.error, equals('Test error'));
      
      // Test error clearing
      final clearedState = updatedState.copyWith(clearError: true);
      expect(clearedState.error, isNull);
      expect(clearedState.isSetupComplete, isTrue); // Should preserve other values
    });

    test('StreamStatus model works correctly', () {
      const status = StreamStatus(
        state: StreamState.streaming,
        bitrate: 2500000,
        frameRate: 30.0,
        droppedFrames: 5,
      );
      
      expect(status.state, StreamState.streaming);
      expect(status.bitrate, 2500000);
      expect(status.frameRate, 30.0);
      expect(status.droppedFrames, 5);
      
      // Test copyWith
      final updatedStatus = status.copyWith(
        state: StreamState.error,
        error: 'Connection lost',
      );
      
      expect(updatedStatus.state, StreamState.error);
      expect(updatedStatus.error, equals('Connection lost'));
      expect(updatedStatus.bitrate, 2500000); // Should preserve other values
    });

    test('StreamSettings model works correctly', () {
      const settings = StreamSettings(
        width: 1920,
        height: 1080,
        frameRate: 60,
        bitrate: 5000000,
        codec: 'h264',
      );
      
      expect(settings.width, 1920);
      expect(settings.height, 1080);
      expect(settings.frameRate, 60);
      expect(settings.bitrate, 5000000);
      expect(settings.codec, 'h264');
      
      // Test JSON serialization
      final json = settings.toJson();
      final fromJson = StreamSettings.fromJson(json);
      
      expect(fromJson.width, settings.width);
      expect(fromJson.height, settings.height);
      expect(fromJson.frameRate, settings.frameRate);
      expect(fromJson.bitrate, settings.bitrate);
      expect(fromJson.codec, settings.codec);
    });
  });
}
