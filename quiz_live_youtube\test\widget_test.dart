import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:quiz_live_youtube/main.dart';
import 'package:quiz_live_youtube/core/constants/app_constants.dart';

void main() {
  testWidgets('App launches successfully', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: QuizLiveYouTubeApp()));

    // Verify that the splash screen appears
    expect(find.text('Quiz Live YouTube'), findsOneWidget);
    expect(find.text('Initializing...'), findsOneWidget);
  });

  group('App Constants Tests', () {
    test('App constants are properly defined', () {
      expect(AppConstants.appName, isNotEmpty);
      expect(AppConstants.defaultStreamWidth, greaterThan(0));
      expect(AppConstants.defaultStreamHeight, greaterThan(0));
      expect(AppConstants.defaultFrameRate, greaterThan(0));
    });
  });
}
