import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quiz_live_youtube/core/utils/color_utils.dart';
import 'package:quiz_live_youtube/core/utils/navigation_utils.dart';
import 'package:quiz_live_youtube/presentation/theme/app_theme.dart';
import 'package:quiz_live_youtube/presentation/widgets/error_widget.dart';

void main() {
  group('Presentation Layer Tests', () {
    testWidgets('AppTheme provides valid light theme', (tester) async {
      final theme = AppTheme.lightTheme;
      
      expect(theme.brightness, Brightness.light);
      expect(theme.colorScheme.primary, isNotNull);
      expect(theme.scaffoldBackgroundColor, isNotNull);
    });

    testWidgets('AppTheme provides valid dark theme', (tester) async {
      final theme = AppTheme.darkTheme;
      
      expect(theme.brightness, Brightness.dark);
      expect(theme.colorScheme.primary, isNotNull);
      expect(theme.scaffoldBackgroundColor, isNotNull);
    });

    test('ColorUtils provides correct opacity', () {
      const color = Colors.blue;
      final result = ColorUtils.withOpacity(color, 0.5);
      
      expect(result.alpha, closeTo(0.5 * 255, 1));
    });

    test('ColorUtils helper methods work', () {
      const color = Colors.red;
      
      final subtle = ColorUtils.subtle(color);
      final semiTransparent = ColorUtils.semiTransparent(color);
      final faded = ColorUtils.faded(color);
      
      expect(subtle.alpha, closeTo(0.7 * 255, 1));
      expect(semiTransparent.alpha, closeTo(0.5 * 255, 1));
      expect(faded.alpha, closeTo(0.3 * 255, 1));
    });

    test('NavigationUtils provides correct routes', () {
      expect(NavigationUtils.splash, '/');
      expect(NavigationUtils.auth, '/auth');
      expect(NavigationUtils.home, '/home');
      expect(NavigationUtils.quizManagement, '/quiz-management');
      expect(NavigationUtils.liveQuiz, '/live-quiz');
      expect(NavigationUtils.streamingSetup, '/streaming-setup');
    });

    testWidgets('AppErrorWidget displays correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppErrorWidget.general(
              message: 'Test error message',
              onRetry: () {},
            ),
          ),
        ),
      );

      expect(find.text('Something went wrong'), findsOneWidget);
      expect(find.text('Test error message'), findsOneWidget);
      expect(find.text('Try Again'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('AppLoadingWidget displays correctly', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AppLoadingWidget(message: 'Loading test'),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading test'), findsOneWidget);
    });

    testWidgets('AppEmptyWidget displays correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppEmptyWidget(
              title: 'Empty State',
              message: 'No items found',
              icon: Icons.inbox,
              action: ElevatedButton(
                onPressed: () {},
                child: const Text('Add Item'),
              ),
            ),
          ),
        ),
      );

      expect(find.text('Empty State'), findsOneWidget);
      expect(find.text('No items found'), findsOneWidget);
      expect(find.text('Add Item'), findsOneWidget);
      expect(find.byIcon(Icons.inbox), findsOneWidget);
    });

    testWidgets('NotFoundScreen displays correctly', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: NotFoundScreen(),
        ),
      );

      expect(find.text('Page Not Found'), findsOneWidget);
      expect(find.text('404'), findsOneWidget);
      expect(find.text('Page not found'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });
  });

  group('Navigation Tests', () {
    test('generateRoute returns correct routes', () {
      final splashRoute = NavigationUtils.generateRoute(
        const RouteSettings(name: NavigationUtils.splash),
      );
      expect(splashRoute, isNotNull);

      final authRoute = NavigationUtils.generateRoute(
        const RouteSettings(name: NavigationUtils.auth),
      );
      expect(authRoute, isNotNull);

      final unknownRoute = NavigationUtils.generateRoute(
        const RouteSettings(name: '/unknown'),
      );
      expect(unknownRoute, isNotNull);
    });
  });

  group('Widget Integration Tests', () {
    testWidgets('Error widgets work with ProviderScope', (tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: AppErrorWidget.network(
                onRetry: () {},
              ),
            ),
          ),
        ),
      );

      expect(find.text('Network Error'), findsOneWidget);
      expect(find.text('Please check your internet connection and try again.'), findsOneWidget);
    });

    testWidgets('Theme integration works correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          home: const Scaffold(
            body: Center(
              child: Text('Theme Test'),
            ),
          ),
        ),
      );

      expect(find.text('Theme Test'), findsOneWidget);
    });
  });
}
