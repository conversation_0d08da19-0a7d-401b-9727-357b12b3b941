# Quiz Live YouTube - Setup Guide

## Prerequisites

1. **Flutter SDK** (3.8.0 or higher)
2. **Android Studio** or **VS Code** with Flutter extensions
3. **YouTube Data API v3** access
4. **Google Cloud Console** project

## YouTube API Setup

### 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the **YouTube Data API v3**

### 2. Create OAuth 2.0 Credentials

1. Go to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth 2.0 Client IDs**
3. Configure the consent screen if prompted
4. Select **Android** as application type
5. Add your package name: `com.example.quiz_live_youtube`
6. Add your SHA-1 certificate fingerprint

### 3. Get SHA-1 Fingerprint

For debug builds:
```bash
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

For release builds:
```bash
keytool -list -v -keystore /path/to/your/keystore.jks -alias your_alias_name
```

### 4. Configure Client ID

Create a `.env` file in the project root:
```env
YOUTUBE_CLIENT_ID=your_client_id_here.apps.googleusercontent.com
YOUTUBE_CLIENT_SECRET=your_client_secret_here
DEBUG_MODE=true
ENABLE_LOGGING=true
```

Or set environment variables when running:
```bash
flutter run --dart-define=YOUTUBE_CLIENT_ID=your_client_id_here
```

## Android Configuration

### 1. Update android/app/build.gradle

Add to `android/app/build.gradle`:
```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
    }
}
```

### 2. Add Permissions

Ensure these permissions are in `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
```

### 3. Screen Capture Permission

Add to `AndroidManifest.xml` in the `<application>` tag:
```xml
<service android:name=".MediaProjectionService" 
         android:foregroundServiceType="mediaProjection" />
```

## Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd quiz_live_youtube
```

2. **Install dependencies**
```bash
flutter pub get
```

3. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your YouTube API credentials
```

4. **Run the app**
```bash
flutter run --dart-define-from-file=.env
```

## Troubleshooting

### Common Issues

1. **"YouTube client ID not configured"**
   - Ensure YOUTUBE_CLIENT_ID environment variable is set
   - Check that the client ID is valid and matches your Google Cloud project

2. **"Authentication failed"**
   - Verify SHA-1 fingerprint is added to OAuth credentials
   - Check that YouTube Data API v3 is enabled
   - Ensure package name matches in Google Cloud Console

3. **"Screen capture permission required"**
   - Grant screen recording permission when prompted
   - Check that MediaProjection service is declared in AndroidManifest.xml

4. **"Failed to start stream"**
   - Verify RTMP URL and stream key are correct
   - Check internet connection
   - Ensure YouTube Live streaming is enabled for your channel

### Debug Mode

Enable debug logging by setting:
```env
DEBUG_MODE=true
ENABLE_LOGGING=true
```

### Logs

Check Flutter logs for detailed error information:
```bash
flutter logs
```

## Testing

Run tests:
```bash
flutter test
```

Run integration tests:
```bash
flutter drive --target=test_driver/app.dart
```

## Building for Release

1. **Create release keystore**
2. **Update android/app/build.gradle** with signing config
3. **Build release APK**
```bash
flutter build apk --release --dart-define-from-file=.env
```

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review Flutter and YouTube API documentation
3. Check project issues on GitHub
