@echo off
echo 🔄 Basculement vers la configuration Gradle simple
echo ==================================================

echo.
echo 📋 Sauvegarde de la configuration actuelle...
echo ----------------------------------------

cd android
if %ERRORLEVEL% neq 0 (
    echo ❌ Dossier android non trouvé
    pause
    exit /b 1
)

REM Sauvegarder la configuration actuelle
if exist "build.gradle.kts" (
    copy "build.gradle.kts" "build.gradle.kts.backup"
    echo ✅ Configuration actuelle sauvegardée: build.gradle.kts.backup
) else (
    echo ⚠️  Aucun build.gradle.kts trouvé
)

echo.
echo 📋 Application de la configuration simple...
echo ----------------------------------------

REM Appliquer la configuration simple
if exist "build.gradle.kts.simple" (
    copy "build.gradle.kts.simple" "build.gradle.kts"
    echo ✅ Configuration simple appliquée
) else (
    echo ❌ Fichier build.gradle.kts.simple non trouvé
    cd ..
    pause
    exit /b 1
)

echo.
echo 📋 Test de la nouvelle configuration...
echo ----------------------------------------

echo Test de syntaxe...
gradlew help --dry-run
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur avec la configuration simple
    echo Restauration de la sauvegarde...
    
    if exist "build.gradle.kts.backup" (
        copy "build.gradle.kts.backup" "build.gradle.kts"
        echo ✅ Configuration restaurée
    )
    
    cd ..
    pause
    exit /b 1
)

echo ✅ Configuration simple fonctionne !

cd ..

echo.
echo 📋 Test Flutter...
echo ----------------------------------------

flutter clean
flutter pub get
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur Flutter avec la configuration simple
    pause
    exit /b 1
)

echo ✅ Flutter fonctionne avec la configuration simple !

echo.
echo 🎉 Configuration simple appliquée avec succès !
echo ===============================================
echo.
echo 📄 Fichiers:
echo - build.gradle.kts.backup (sauvegarde)
echo - build.gradle.kts (configuration simple active)
echo - build.gradle.kts.simple (modèle)
echo.
echo 🎯 Prochaine étape: flutter build apk --debug
echo.

pause
