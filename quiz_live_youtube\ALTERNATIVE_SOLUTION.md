# Solution Alternative : FFmpeg Kit Direct

## Problème Persistant

Si l'erreur `Could not find com.arthenica:ffmpeg-kit-https:6.0-2` persiste malgré la configuration de substitution, voici une solution alternative qui utilise directement le package mrljdx.

## Solution Alternative 1 : Package Direct

### 1. Supprimer le Plugin Flutter

```yaml
# pubspec.yaml - SUPPRIMER cette ligne
# ffmpeg_kit_flutter: 6.0.3
# ffmpeg_kit_flutter_https: 6.0.3
```

### 2. Utiliser Uniquement Android Native

```kotlin
// android/app/build.gradle.kts
dependencies {
    // FFmpeg Kit - Version mrljdx directe
    implementation("com.mrljdx:ffmpeg-kit-full:6.0")
    
    // Dépendances Android standard
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.appcompat:appcompat:1.6.1")
}
```

### 3. <PERSON><PERSON><PERSON> un Wrapper Dart

```dart
// lib/core/services/ffmpeg_native_service.dart
import 'package:flutter/services.dart';

class FFmpegNativeService {
  static const MethodChannel _channel = MethodChannel('ffmpeg_native');
  
  static Future<bool> execute(String command) async {
    try {
      final result = await _channel.invokeMethod('execute', {'command': command});
      return result == true;
    } catch (e) {
      print('Erreur FFmpeg: $e');
      return false;
    }
  }
  
  static Future<String> getVersion() async {
    try {
      final result = await _channel.invokeMethod('getVersion');
      return result ?? 'Unknown';
    } catch (e) {
      return 'Error: $e';
    }
  }
}
```

### 4. Implémentation Android Native

```kotlin
// android/app/src/main/kotlin/MainActivity.kt
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import com.arthenica.ffmpegkit.FFmpegKit
import com.arthenica.ffmpegkit.ReturnCode

class MainActivity: FlutterActivity() {
    private val CHANNEL = "ffmpeg_native"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
            .setMethodCallHandler { call, result ->
                when (call.method) {
                    "execute" -> {
                        val command = call.argument<String>("command")
                        if (command != null) {
                            executeFFmpeg(command, result)
                        } else {
                            result.error("INVALID_ARGUMENT", "Command is null", null)
                        }
                    }
                    "getVersion" -> {
                        getFFmpegVersion(result)
                    }
                    else -> {
                        result.notImplemented()
                    }
                }
            }
    }
    
    private fun executeFFmpeg(command: String, result: MethodChannel.Result) {
        try {
            val session = FFmpegKit.execute(command)
            val returnCode = session.returnCode
            
            if (ReturnCode.isSuccess(returnCode)) {
                result.success(true)
            } else {
                result.success(false)
            }
        } catch (e: Exception) {
            result.error("FFMPEG_ERROR", e.message, null)
        }
    }
    
    private fun getFFmpegVersion(result: MethodChannel.Result) {
        try {
            val session = FFmpegKit.execute("-version")
            val logs = session.allLogs
            
            if (logs.isNotEmpty()) {
                val version = logs[0].message
                result.success(version)
            } else {
                result.success("No version info")
            }
        } catch (e: Exception) {
            result.error("VERSION_ERROR", e.message, null)
        }
    }
}
```

## Solution Alternative 2 : Fork du Plugin

### 1. Créer un Fork Local

```yaml
# pubspec.yaml
dependencies:
  ffmpeg_kit_flutter:
    path: ./local_plugins/ffmpeg_kit_flutter_mrljdx
```

### 2. Modifier le Plugin

Créer `local_plugins/ffmpeg_kit_flutter_mrljdx/android/build.gradle` :

```gradle
dependencies {
    implementation 'com.mrljdx:ffmpeg-kit-full:6.0'
    // Pas de dépendance arthenica
}
```

## Solution Alternative 3 : Gradle Plugin

### 1. Créer un Plugin Gradle

```kotlin
// android/buildSrc/src/main/kotlin/FFmpegSubstitutionPlugin.kt
import org.gradle.api.Plugin
import org.gradle.api.Project

class FFmpegSubstitutionPlugin : Plugin<Project> {
    override fun apply(project: Project) {
        project.configurations.all { configuration ->
            configuration.resolutionStrategy.dependencySubstitution {
                substitute(module("com.arthenica:ffmpeg-kit-https"))
                    .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
                substitute(module("com.arthenica:ffmpeg-kit-https:6.0-2"))
                    .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
            }
        }
    }
}
```

### 2. Appliquer le Plugin

```kotlin
// android/build.gradle.kts
plugins {
    id("ffmpeg-substitution")
}
```

## Solution Alternative 4 : Exclusion Complète

### 1. Exclure Toutes les Dépendances Arthenica

```kotlin
// android/app/build.gradle.kts
configurations.all {
    exclude(group = "com.arthenica")
}

dependencies {
    implementation("com.mrljdx:ffmpeg-kit-full:6.0")
}
```

### 2. Forcer la Résolution

```kotlin
configurations.all {
    resolutionStrategy {
        force("com.mrljdx:ffmpeg-kit-full:6.0")
        
        eachDependency { details ->
            if (details.requested.group == "com.arthenica") {
                details.useTarget("com.mrljdx:ffmpeg-kit-full:6.0")
            }
        }
    }
}
```

## Test des Solutions

### Script de Test

```bash
# test_alternatives.bat
@echo off
echo Test des solutions alternatives...

echo Test 1: Build sans plugin Flutter
flutter build apk --debug

echo Test 2: Vérification des dépendances
cd android
gradlew app:dependencies | findstr ffmpeg

echo Test 3: Test natif
dart test_native_ffmpeg.dart
```

### Test Dart

```dart
// test_native_ffmpeg.dart
import 'package:flutter/services.dart';

void main() async {
  try {
    const channel = MethodChannel('ffmpeg_native');
    final version = await channel.invokeMethod('getVersion');
    print('Version FFmpeg: $version');
    
    final result = await channel.invokeMethod('execute', {
      'command': '-f lavfi -i testsrc=duration=1:size=320x240:rate=1 -f null -'
    });
    print('Test réussi: $result');
  } catch (e) {
    print('Erreur: $e');
  }
}
```

## Recommandation

1. **Essayez d'abord** la solution de substitution globale dans `android/build.gradle.kts`
2. **Si ça échoue**, utilisez la Solution Alternative 1 (package direct + wrapper)
3. **En dernier recours**, créez un fork local du plugin

## Avantages des Alternatives

- ✅ **Contrôle total** sur les dépendances
- ✅ **Pas de conflit** avec les packages officiels
- ✅ **Performance** optimale
- ✅ **Debugging** plus facile

## Inconvénients

- ❌ **Plus de code** à maintenir
- ❌ **Pas de mises à jour** automatiques du plugin
- ❌ **Implémentation native** requise

Choisissez la solution qui convient le mieux à votre projet et à vos contraintes de maintenance.
