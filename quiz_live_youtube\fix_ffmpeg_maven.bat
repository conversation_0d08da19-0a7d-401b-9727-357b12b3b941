@echo off
echo 🔧 Correction de la configuration FFmpeg Kit Maven (mrljdx)
echo =========================================================

echo.
echo 📋 Étape 1: Nettoyage des caches...
echo ----------------------------------------

REM Nettoyer le cache Flutter
echo Nettoyage du cache Flutter...
flutter clean
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors du nettoyage Flutter
    pause
    exit /b 1
)

REM Nettoyer le cache Pub
echo Nettoyage du cache Pub...
flutter pub cache clean
if %ERRORLEVEL% neq 0 (
    echo ⚠️  Avertissement: Erreur lors du nettoyage du cache Pub
)

echo.
echo 📋 Étape 2: Nettoyage Gradle...
echo ----------------------------------------

REM Aller dans le dossier Android
cd android
if %ERRORLEVEL% neq 0 (
    echo ❌ Dossier android non trouvé
    pause
    exit /b 1
)

REM Nettoyer Gradle
echo Nettoyage Gradle...
gradlew clean
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors du nettoyage Gradle
    cd ..
    pause
    exit /b 1
)

REM Rafraîchir les dépendances
echo Rafraîchissement des dépendances...
gradlew --refresh-dependencies
if %ERRORLEVEL% neq 0 (
    echo ⚠️  Avertissement: Erreur lors du rafraîchissement des dépendances
)

REM Retourner au dossier racine
cd ..

echo.
echo 📋 Étape 3: Installation des dépendances Flutter...
echo ----------------------------------------

REM Installer les dépendances Flutter
echo Installation des dépendances Flutter...
flutter pub get
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors de l'installation des dépendances Flutter
    pause
    exit /b 1
)

echo.
echo 📋 Étape 4: Test de build Android...
echo ----------------------------------------

REM Tester le build Android
echo Test du build Android...
flutter build apk --debug
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors du build Android
    echo.
    echo 🔍 Vérifications suggérées:
    echo 1. Vérifiez que Maven Central est accessible
    echo 2. Vérifiez la configuration dans android/app/build.gradle.kts
    echo 3. Consultez les logs d'erreur ci-dessus
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Configuration FFmpeg Kit Maven (mrljdx) réussie !
echo =====================================================
echo.
echo 📦 Package utilisé: com.mrljdx:ffmpeg-kit-full:6.0
echo 🔗 Source: https://repo1.maven.org/maven2/com/mrljdx/ffmpeg-kit-full/6.0/
echo.
echo 🎯 Prochaines étapes:
echo 1. Testez l'application: flutter run
echo 2. Vérifiez les fonctionnalités FFmpeg
echo 3. Consultez les logs pour confirmer l'utilisation de la version mrljdx
echo.

pause
