# Migration vers FFmpeg Kit

## Changements Apportés

Ce document décrit les modifications apportées pour utiliser le package `ffmpeg-kit` personnalisé au lieu de `ffmpeg-kit-full`.

### 1. Modification du pubspec.yaml

**Avant :**
```yaml
ffmpeg_kit_flutter: ^6.0.3
```

**Après :**
```yaml
ffmpeg_kit:
  git:
    url: https://github.com/mrljdx/ffmpeg-kit.git
    path: flutter/ffmpeg_kit_flutter
```

### 2. Modifications du StreamingService

#### Changements Principaux :
- **Suppression du MethodChannel** : Remplacement de l'approche native par FFmpeg Kit
- **Nouvelle approche de streaming** : Utilisation directe de FFmpeg pour le streaming RTMP
- **Gestion des sessions** : Utilisation de `FFmpegSession` pour contrôler les streams

#### Méthodes Modifiées :

##### `initialize()`
- Suppression de l'initialisation du MethodChannel
- Ajout de l'initialisation FFmpeg Kit
- Configuration des callbacks de log

##### `startStreaming()`
- Remplacement des appels MethodChannel par `FFmpegKit.executeAsync()`
- Construction dynamique des commandes FFmpeg
- Gestion des callbacks de session, logs et statistiques

##### `stopStreaming()`
- Utilisation de `session.cancel()` au lieu des appels natifs
- Nettoyage des sessions FFmpeg

##### `pauseStreaming()` et `resumeStreaming()`
- **Note importante** : FFmpeg ne supporte pas nativement pause/resume
- Ces méthodes nécessitent un arrêt/redémarrage du stream

##### `updateStreamSettings()`
- **Note importante** : Changement des paramètres nécessite un redémarrage du stream avec FFmpeg

#### Nouvelle Méthode : `_buildFFmpegCommand()`

Cette méthode construit la commande FFmpeg pour le streaming RTMP :

```dart
String _buildFFmpegCommand(String rtmpUrl, StreamSettings settings) {
  final List<String> args = [
    // Input settings (exemple avec source de test)
    '-f', 'lavfi',
    '-i', 'testsrc=size=${settings.width}x${settings.height}:rate=${settings.frameRate}',
    
    // Encodage vidéo
    '-c:v', settings.codec,
    '-b:v', '${settings.bitrate}',
    '-maxrate', '${settings.bitrate}',
    '-bufsize', '${settings.bitrate * 2}',
    '-preset', 'ultrafast',
    '-tune', 'zerolatency',
    
    // Paramètres audio
    '-f', 'lavfi',
    '-i', 'anullsrc=channel_layout=stereo:sample_rate=44100',
    '-c:a', 'aac',
    '-b:a', '128k',
    
    // Sortie RTMP
    '-f', 'flv',
    rtmpUrl,
  ];
  
  return args.join(' ');
}
```

### 3. Limitations et Considérations

#### Capture d'Écran
- **Android** : Nécessite l'implémentation de MediaProjection API
- **iOS** : Nécessite l'implémentation de ReplayKit
- **Solution actuelle** : Utilise une source de test (`testsrc`) pour la démonstration

#### Fonctionnalités Limitées
1. **Pause/Resume** : Non supporté nativement par FFmpeg
2. **Mise à jour des paramètres en temps réel** : Nécessite un redémarrage
3. **Capture d'écran** : Doit être implémentée au niveau plateforme

### 4. Prochaines Étapes

#### Pour une Implémentation Complète :

1. **Implémentation de la Capture d'Écran**
   ```dart
   // Android - MediaProjection
   // iOS - ReplayKit
   // Pipe vers FFmpeg
   ```

2. **Optimisation des Commandes FFmpeg**
   ```dart
   // Paramètres optimisés pour le streaming en direct
   '-preset', 'ultrafast',
   '-tune', 'zerolatency',
   '-g', '60', // GOP size
   '-keyint_min', '60',
   ```

3. **Gestion Avancée des Erreurs**
   ```dart
   // Retry automatique
   // Détection de déconnexion
   // Reconnexion automatique
   ```

4. **Monitoring des Performances**
   ```dart
   // Statistiques en temps réel
   // Qualité du stream
   // Latence
   ```

### 5. Configuration Requise

#### Permissions Android
```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
```

#### Permissions iOS
```xml
<key>NSCameraUsageDescription</key>
<string>Cette app a besoin d'accéder à la caméra pour le streaming</string>
<key>NSMicrophoneUsageDescription</key>
<string>Cette app a besoin d'accéder au microphone pour le streaming</string>
```

### 6. Avantages de FFmpeg Kit

1. **Flexibilité** : Contrôle total sur les paramètres de streaming
2. **Performance** : Optimisations FFmpeg natives
3. **Compatibilité** : Support étendu des formats et codecs
4. **Personnalisation** : Commandes FFmpeg sur mesure

### 7. Inconvénients

1. **Complexité** : Nécessite une connaissance approfondie de FFmpeg
2. **Taille** : Package plus volumineux
3. **Capture d'écran** : Implémentation plateforme requise
4. **Debugging** : Plus difficile à déboguer que les solutions natives

## Conclusion

La migration vers `ffmpeg-kit` offre plus de flexibilité et de contrôle, mais nécessite une implémentation plus complexe, particulièrement pour la capture d'écran. Le code actuel fournit une base solide qui peut être étendue selon les besoins spécifiques du projet.
