/// YouTube authentication token information
class YouTubeToken {
  final String accessToken;
  final String refreshToken;
  final String tokenType;
  final int expiresIn;
  final List<String> scopes;
  final DateTime? expiresAt;

  const YouTubeToken({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
    required this.scopes,
    this.expiresAt,
  });

  YouTubeToken copyWith({
    String? accessToken,
    String? refreshToken,
    String? tokenType,
    int? expiresIn,
    List<String>? scopes,
    DateTime? expiresAt,
  }) {
    return YouTubeToken(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      tokenType: tokenType ?? this.tokenType,
      expiresIn: expiresIn ?? this.expiresIn,
      scopes: scopes ?? this.scopes,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'tokenType': tokenType,
      'expiresIn': expiresIn,
      'scopes': scopes,
      'expiresAt': expiresAt?.toIso8601String(),
    };
  }

  factory YouTubeToken.fromJson(Map<String, dynamic> json) {
    return YouTubeToken(
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String,
      tokenType: json['tokenType'] as String,
      expiresIn: json['expiresIn'] as int,
      scopes: List<String>.from(json['scopes'] as List),
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
    );
  }
}

/// YouTube live broadcast information
class YouTubeLiveBroadcast {
  final String id;
  final String title;
  final String description;
  final String status;
  final String privacyStatus;
  final String? streamKey;
  final String? streamUrl;
  final String? chatId;
  final DateTime? scheduledStartTime;
  final DateTime? actualStartTime;
  final DateTime? actualEndTime;

  const YouTubeLiveBroadcast({
    required this.id,
    required this.title,
    required this.description,
    required this.status,
    required this.privacyStatus,
    this.streamKey,
    this.streamUrl,
    this.chatId,
    this.scheduledStartTime,
    this.actualStartTime,
    this.actualEndTime,
  });

  YouTubeLiveBroadcast copyWith({
    String? id,
    String? title,
    String? description,
    String? status,
    String? privacyStatus,
    String? streamKey,
    String? streamUrl,
    String? chatId,
    DateTime? scheduledStartTime,
    DateTime? actualStartTime,
    DateTime? actualEndTime,
  }) {
    return YouTubeLiveBroadcast(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      privacyStatus: privacyStatus ?? this.privacyStatus,
      streamKey: streamKey ?? this.streamKey,
      streamUrl: streamUrl ?? this.streamUrl,
      chatId: chatId ?? this.chatId,
      scheduledStartTime: scheduledStartTime ?? this.scheduledStartTime,
      actualStartTime: actualStartTime ?? this.actualStartTime,
      actualEndTime: actualEndTime ?? this.actualEndTime,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'status': status,
      'privacyStatus': privacyStatus,
      'streamKey': streamKey,
      'streamUrl': streamUrl,
      'chatId': chatId,
      'scheduledStartTime': scheduledStartTime?.toIso8601String(),
      'actualStartTime': actualStartTime?.toIso8601String(),
      'actualEndTime': actualEndTime?.toIso8601String(),
    };
  }

  factory YouTubeLiveBroadcast.fromJson(Map<String, dynamic> json) {
    return YouTubeLiveBroadcast(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      status: json['status'] as String,
      privacyStatus: json['privacyStatus'] as String,
      streamKey: json['streamKey'] as String?,
      streamUrl: json['streamUrl'] as String?,
      chatId: json['chatId'] as String?,
      scheduledStartTime: json['scheduledStartTime'] != null
          ? DateTime.parse(json['scheduledStartTime'] as String)
          : null,
      actualStartTime: json['actualStartTime'] != null
          ? DateTime.parse(json['actualStartTime'] as String)
          : null,
      actualEndTime: json['actualEndTime'] != null
          ? DateTime.parse(json['actualEndTime'] as String)
          : null,
    );
  }
}

/// YouTube live stream information
class YouTubeLiveStream {
  final String id;
  final String title;
  final String status;
  final StreamSettings settings;
  final String? ingestionAddress;
  final String? streamName;

  const YouTubeLiveStream({
    required this.id,
    required this.title,
    required this.status,
    required this.settings,
    this.ingestionAddress,
    this.streamName,
  });

  YouTubeLiveStream copyWith({
    String? id,
    String? title,
    String? status,
    StreamSettings? settings,
    String? ingestionAddress,
    String? streamName,
  }) {
    return YouTubeLiveStream(
      id: id ?? this.id,
      title: title ?? this.title,
      status: status ?? this.status,
      settings: settings ?? this.settings,
      ingestionAddress: ingestionAddress ?? this.ingestionAddress,
      streamName: streamName ?? this.streamName,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'status': status,
      'settings': settings.toJson(),
      'ingestionAddress': ingestionAddress,
      'streamName': streamName,
    };
  }

  factory YouTubeLiveStream.fromJson(Map<String, dynamic> json) {
    return YouTubeLiveStream(
      id: json['id'] as String,
      title: json['title'] as String,
      status: json['status'] as String,
      settings: StreamSettings.fromJson(json['settings'] as Map<String, dynamic>),
      ingestionAddress: json['ingestionAddress'] as String?,
      streamName: json['streamName'] as String?,
    );
  }
}

/// Stream quality settings
class StreamSettings {
  final int width;
  final int height;
  final int frameRate;
  final int bitrate;
  final String codec;

  const StreamSettings({
    this.width = 1280,
    this.height = 720,
    this.frameRate = 30,
    this.bitrate = 2500000,
    this.codec = 'h264',
  });

  StreamSettings copyWith({
    int? width,
    int? height,
    int? frameRate,
    int? bitrate,
    String? codec,
  }) {
    return StreamSettings(
      width: width ?? this.width,
      height: height ?? this.height,
      frameRate: frameRate ?? this.frameRate,
      bitrate: bitrate ?? this.bitrate,
      codec: codec ?? this.codec,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'width': width,
      'height': height,
      'frameRate': frameRate,
      'bitrate': bitrate,
      'codec': codec,
    };
  }

  factory StreamSettings.fromJson(Map<String, dynamic> json) {
    return StreamSettings(
      width: json['width'] as int? ?? 1280,
      height: json['height'] as int? ?? 720,
      frameRate: json['frameRate'] as int? ?? 30,
      bitrate: json['bitrate'] as int? ?? 2500000,
      codec: json['codec'] as String? ?? 'h264',
    );
  }
}

/// YouTube chat message
class YouTubeChatMessage {
  final String id;
  final String authorChannelId;
  final String authorDisplayName;
  final String messageText;
  final DateTime publishedAt;
  final bool isFromOwner;
  final bool isModerator;
  final bool isSponsor;

  const YouTubeChatMessage({
    required this.id,
    required this.authorChannelId,
    required this.authorDisplayName,
    required this.messageText,
    required this.publishedAt,
    this.isFromOwner = false,
    this.isModerator = false,
    this.isSponsor = false,
  });

  YouTubeChatMessage copyWith({
    String? id,
    String? authorChannelId,
    String? authorDisplayName,
    String? messageText,
    DateTime? publishedAt,
    bool? isFromOwner,
    bool? isModerator,
    bool? isSponsor,
  }) {
    return YouTubeChatMessage(
      id: id ?? this.id,
      authorChannelId: authorChannelId ?? this.authorChannelId,
      authorDisplayName: authorDisplayName ?? this.authorDisplayName,
      messageText: messageText ?? this.messageText,
      publishedAt: publishedAt ?? this.publishedAt,
      isFromOwner: isFromOwner ?? this.isFromOwner,
      isModerator: isModerator ?? this.isModerator,
      isSponsor: isSponsor ?? this.isSponsor,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'authorChannelId': authorChannelId,
      'authorDisplayName': authorDisplayName,
      'messageText': messageText,
      'publishedAt': publishedAt.toIso8601String(),
      'isFromOwner': isFromOwner,
      'isModerator': isModerator,
      'isSponsor': isSponsor,
    };
  }

  factory YouTubeChatMessage.fromJson(Map<String, dynamic> json) {
    return YouTubeChatMessage(
      id: json['id'] as String,
      authorChannelId: json['authorChannelId'] as String,
      authorDisplayName: json['authorDisplayName'] as String,
      messageText: json['messageText'] as String,
      publishedAt: DateTime.parse(json['publishedAt'] as String),
      isFromOwner: json['isFromOwner'] as bool? ?? false,
      isModerator: json['isModerator'] as bool? ?? false,
      isSponsor: json['isSponsor'] as bool? ?? false,
    );
  }
}

/// YouTube channel information
class YouTubeChannel {
  final String id;
  final String title;
  final String description;
  final String? thumbnailUrl;
  final int? subscriberCount;
  final int? videoCount;

  const YouTubeChannel({
    required this.id,
    required this.title,
    required this.description,
    this.thumbnailUrl,
    this.subscriberCount,
    this.videoCount,
  });

  YouTubeChannel copyWith({
    String? id,
    String? title,
    String? description,
    String? thumbnailUrl,
    int? subscriberCount,
    int? videoCount,
  }) {
    return YouTubeChannel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      subscriberCount: subscriberCount ?? this.subscriberCount,
      videoCount: videoCount ?? this.videoCount,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'thumbnailUrl': thumbnailUrl,
      'subscriberCount': subscriberCount,
      'videoCount': videoCount,
    };
  }

  factory YouTubeChannel.fromJson(Map<String, dynamic> json) {
    return YouTubeChannel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      subscriberCount: json['subscriberCount'] as int?,
      videoCount: json['videoCount'] as int?,
    );
  }
}

/// Stream status information
class StreamStatus {
  final StreamState state;
  final int bitrate;
  final double frameRate;
  final int droppedFrames;
  final String? error;
  final DateTime? lastUpdate;

  const StreamStatus({
    this.state = StreamState.disconnected,
    this.bitrate = 0,
    this.frameRate = 0.0,
    this.droppedFrames = 0,
    this.error,
    this.lastUpdate,
  });

  StreamStatus copyWith({
    StreamState? state,
    int? bitrate,
    double? frameRate,
    int? droppedFrames,
    String? error,
    DateTime? lastUpdate,
  }) {
    return StreamStatus(
      state: state ?? this.state,
      bitrate: bitrate ?? this.bitrate,
      frameRate: frameRate ?? this.frameRate,
      droppedFrames: droppedFrames ?? this.droppedFrames,
      error: error ?? this.error,
      lastUpdate: lastUpdate ?? this.lastUpdate,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state.name,
      'bitrate': bitrate,
      'frameRate': frameRate,
      'droppedFrames': droppedFrames,
      'error': error,
      'lastUpdate': lastUpdate?.toIso8601String(),
    };
  }

  factory StreamStatus.fromJson(Map<String, dynamic> json) {
    return StreamStatus(
      state: StreamState.values.firstWhere(
        (s) => s.name == json['state'],
        orElse: () => StreamState.disconnected,
      ),
      bitrate: json['bitrate'] as int? ?? 0,
      frameRate: (json['frameRate'] as num?)?.toDouble() ?? 0.0,
      droppedFrames: json['droppedFrames'] as int? ?? 0,
      error: json['error'] as String?,
      lastUpdate: json['lastUpdate'] != null
          ? DateTime.parse(json['lastUpdate'] as String)
          : null,
    );
  }
}

/// Stream connection states
enum StreamState {
  disconnected,
  connecting,
  connected,
  streaming,
  error,
  reconnecting,
}
