import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/providers/streaming_providers.dart';
import '../../core/models/youtube_models.dart';

/// Screen for setting up YouTube Live streaming
class StreamingSetupScreen extends ConsumerStatefulWidget {
  const StreamingSetupScreen({super.key});

  @override
  ConsumerState<StreamingSetupScreen> createState() => _StreamingSetupScreenState();
}

class _StreamingSetupScreenState extends ConsumerState<StreamingSetupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  StreamSettings _streamSettings = const StreamSettings();
  bool _isSettingUp = false;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final streamingController = ref.watch(streamingControllerProvider);
    final isStreaming = ref.watch(isStreamingProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Streaming Setup'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current Status
              _buildStatusCard(theme, streamingController, isStreaming),
              
              const SizedBox(height: 24),
              
              // Broadcast Settings
              if (!streamingController.isSetupComplete) ...[
                _buildBroadcastSettings(theme),
                const SizedBox(height: 24),
                _buildStreamSettings(theme),
                const SizedBox(height: 24),
              ],
              
              // Actions
              _buildActions(theme, streamingController, isStreaming),
              
              // Error Display
              if (streamingController.error != null) ...[
                const SizedBox(height: 16),
                _buildErrorCard(theme, streamingController.error!),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard(ThemeData theme, dynamic streamingController, bool isStreaming) {
    Color statusColor;
    IconData statusIcon;
    String statusText;
    
    if (isStreaming) {
      statusColor = Colors.red;
      statusIcon = Icons.live_tv;
      statusText = 'Currently Streaming Live';
    } else if (streamingController.isSetupComplete) {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
      statusText = 'Ready to Stream';
    } else {
      statusColor = Colors.orange;
      statusIcon = Icons.settings;
      statusText = 'Setup Required';
    }
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              statusIcon,
              color: statusColor,
              size: 32,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Streaming Status',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    statusText,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBroadcastSettings(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Broadcast Settings',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Stream Title',
                hintText: 'Enter your live stream title',
                prefixIcon: Icon(Icons.title),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a stream title';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'Describe your live stream',
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a description';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStreamSettings(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Stream Quality Settings',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Resolution
            DropdownButtonFormField<String>(
              value: '${_streamSettings.width}x${_streamSettings.height}',
              decoration: const InputDecoration(
                labelText: 'Resolution',
                prefixIcon: Icon(Icons.aspect_ratio),
              ),
              items: const [
                DropdownMenuItem(value: '1920x1080', child: Text('1920x1080 (Full HD)')),
                DropdownMenuItem(value: '1280x720', child: Text('1280x720 (HD)')),
                DropdownMenuItem(value: '854x480', child: Text('854x480 (SD)')),
              ],
              onChanged: (value) {
                if (value != null) {
                  final parts = value.split('x');
                  setState(() {
                    _streamSettings = _streamSettings.copyWith(
                      width: int.parse(parts[0]),
                      height: int.parse(parts[1]),
                    );
                  });
                }
              },
            ),
            
            const SizedBox(height: 16),
            
            // Frame Rate
            DropdownButtonFormField<int>(
              value: _streamSettings.frameRate,
              decoration: const InputDecoration(
                labelText: 'Frame Rate',
                prefixIcon: Icon(Icons.video_camera_back),
              ),
              items: const [
                DropdownMenuItem(value: 30, child: Text('30 FPS')),
                DropdownMenuItem(value: 60, child: Text('60 FPS')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _streamSettings = _streamSettings.copyWith(frameRate: value);
                  });
                }
              },
            ),
            
            const SizedBox(height: 16),
            
            // Bitrate
            DropdownButtonFormField<int>(
              value: _streamSettings.bitrate,
              decoration: const InputDecoration(
                labelText: 'Bitrate',
                prefixIcon: Icon(Icons.speed),
              ),
              items: const [
                DropdownMenuItem(value: 1500000, child: Text('1.5 Mbps (Low)')),
                DropdownMenuItem(value: 2500000, child: Text('2.5 Mbps (Medium)')),
                DropdownMenuItem(value: 4000000, child: Text('4.0 Mbps (High)')),
                DropdownMenuItem(value: 6000000, child: Text('6.0 Mbps (Ultra)')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _streamSettings = _streamSettings.copyWith(bitrate: value);
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActions(ThemeData theme, dynamic streamingController, bool isStreaming) {
    return Column(
      children: [
        if (!streamingController.isSetupComplete) ...[
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton.icon(
              onPressed: _isSettingUp ? null : _setupStreaming,
              icon: _isSettingUp
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.settings),
              label: Text(_isSettingUp ? 'Setting up...' : 'Setup Streaming'),
            ),
          ),
        ] else if (!isStreaming) ...[
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton.icon(
              onPressed: _startStreaming,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Start Streaming'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ] else ...[
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton.icon(
              onPressed: _stopStreaming,
              icon: const Icon(Icons.stop),
              label: const Text('Stop Streaming'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.error,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
        
        if (streamingController.isSetupComplete) ...[
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: TextButton.icon(
              onPressed: _resetSetup,
              icon: const Icon(Icons.refresh),
              label: const Text('Reset Setup'),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildErrorCard(ThemeData theme, String error) {
    return Card(
      color: theme.colorScheme.errorContainer,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.error_outline,
              color: theme.colorScheme.onErrorContainer,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Error',
                    style: theme.textTheme.titleSmall?.copyWith(
                      color: theme.colorScheme.onErrorContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    error,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onErrorContainer,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _setupStreaming() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() {
      _isSettingUp = true;
    });
    
    try {
      await ref.read(streamingControllerProvider.notifier).setupStreaming(
        title: _titleController.text,
        description: _descriptionController.text,
        settings: _streamSettings,
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Streaming setup completed!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Setup failed: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSettingUp = false;
        });
      }
    }
  }

  Future<void> _startStreaming() async {
    try {
      await ref.read(streamingControllerProvider.notifier).startStreaming();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Streaming started!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to start streaming: $e')),
        );
      }
    }
  }

  Future<void> _stopStreaming() async {
    try {
      await ref.read(streamingControllerProvider.notifier).stopStreaming();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Streaming stopped!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to stop streaming: $e')),
        );
      }
    }
  }

  void _resetSetup() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Setup'),
        content: const Text('Are you sure you want to reset the streaming setup?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Reset streaming setup
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Setup reset!')),
              );
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
