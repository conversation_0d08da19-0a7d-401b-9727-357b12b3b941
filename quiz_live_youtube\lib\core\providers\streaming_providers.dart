import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/youtube_models.dart';
import '../services/youtube_api_service.dart';
import '../services/youtube_chat_service.dart';
import '../services/streaming_service.dart';
import 'service_providers.dart';

/// Provider for streaming status
final streamingStatusProvider = StreamProvider<StreamStatus>((ref) {
  final streamingService = ref.watch(streamingServiceProvider);
  return streamingService.statusStream;
});

/// Provider for current stream status
final currentStreamStatusProvider = Provider<StreamStatus>((ref) {
  final streamingService = ref.watch(streamingServiceProvider);
  return streamingService.currentStatus;
});

/// Provider for checking if streaming is active
final isStreamingProvider = Provider<bool>((ref) {
  final status = ref.watch(currentStreamStatusProvider);
  return status.state == StreamState.streaming;
});

/// Provider for live broadcast management
final liveBroadcastProvider = StateNotifierProvider<LiveBroadcastNotifier, AsyncValue<YouTubeLiveBroadcast?>>((ref) {
  final apiService = ref.watch(youtubeApiServiceProvider);
  return LiveBroadcastNotifier(apiService);
});

/// Provider for live stream management
final liveStreamProvider = StateNotifierProvider<LiveStreamNotifier, AsyncValue<YouTubeLiveStream?>>((ref) {
  final apiService = ref.watch(youtubeApiServiceProvider);
  return LiveStreamNotifier(apiService);
});

/// Provider for YouTube chat messages
final chatMessagesProvider = StreamProvider<List<YouTubeChatMessage>>((ref) {
  final chatService = ref.watch(youtubeChatServiceProvider);
  return chatService.chatMessages;
});

/// Provider for chat monitoring status
final isChatMonitoringProvider = Provider<bool>((ref) {
  final chatService = ref.watch(youtubeChatServiceProvider);
  return chatService.isMonitoring;
});

/// State notifier for live broadcast management
class LiveBroadcastNotifier extends StateNotifier<AsyncValue<YouTubeLiveBroadcast?>> {
  final YouTubeApiService _apiService;
  
  LiveBroadcastNotifier(this._apiService) : super(const AsyncValue.data(null));
  
  /// Create a new live broadcast
  Future<void> createBroadcast({
    required String title,
    required String description,
    DateTime? scheduledStartTime,
    String privacyStatus = 'public',
  }) async {
    state = const AsyncValue.loading();
    
    try {
      final broadcast = await _apiService.createLiveBroadcast(
        title: title,
        description: description,
        scheduledStartTime: scheduledStartTime,
        privacyStatus: privacyStatus,
      );
      
      state = AsyncValue.data(broadcast);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
  
  /// Start the live broadcast
  Future<void> startBroadcast() async {
    final currentBroadcast = state.value;
    if (currentBroadcast == null) {
      throw Exception('No broadcast to start');
    }
    
    try {
      await _apiService.startLiveBroadcast(currentBroadcast.id);
      
      // Update broadcast status
      final updatedBroadcast = currentBroadcast.copyWith(
        status: 'live',
        actualStartTime: DateTime.now(),
      );
      
      state = AsyncValue.data(updatedBroadcast);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
  
  /// Stop the live broadcast
  Future<void> stopBroadcast() async {
    final currentBroadcast = state.value;
    if (currentBroadcast == null) {
      throw Exception('No broadcast to stop');
    }
    
    try {
      await _apiService.stopLiveBroadcast(currentBroadcast.id);
      
      // Update broadcast status
      final updatedBroadcast = currentBroadcast.copyWith(
        status: 'complete',
        actualEndTime: DateTime.now(),
      );
      
      state = AsyncValue.data(updatedBroadcast);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
  
  /// Clear current broadcast
  void clearBroadcast() {
    state = const AsyncValue.data(null);
  }
}

/// State notifier for live stream management
class LiveStreamNotifier extends StateNotifier<AsyncValue<YouTubeLiveStream?>> {
  final YouTubeApiService _apiService;
  
  LiveStreamNotifier(this._apiService) : super(const AsyncValue.data(null));
  
  /// Create a new live stream
  Future<void> createStream({
    required String title,
    StreamSettings? settings,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      final stream = await _apiService.createLiveStream(
        title: title,
        settings: settings ?? const StreamSettings(),
      );
      
      state = AsyncValue.data(stream);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
  
  /// Bind stream to broadcast
  Future<void> bindToBroadcast(String broadcastId) async {
    final currentStream = state.value;
    if (currentStream == null) {
      throw Exception('No stream to bind');
    }
    
    try {
      await _apiService.bindBroadcastToStream(broadcastId, currentStream.id);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
  
  /// Clear current stream
  void clearStream() {
    state = const AsyncValue.data(null);
  }
}

/// Provider for streaming controller
final streamingControllerProvider = StateNotifierProvider<StreamingControllerNotifier, StreamingControllerState>((ref) {
  final streamingService = ref.watch(streamingServiceProvider);
  final chatService = ref.watch(youtubeChatServiceProvider);
  final apiService = ref.watch(youtubeApiServiceProvider);
  
  return StreamingControllerNotifier(streamingService, chatService, apiService);
});

/// State for streaming controller
class StreamingControllerState {
  final bool isSetupComplete;
  final String? error;
  final YouTubeLiveBroadcast? broadcast;
  final YouTubeLiveStream? stream;
  final String? chatId;
  
  const StreamingControllerState({
    this.isSetupComplete = false,
    this.error,
    this.broadcast,
    this.stream,
    this.chatId,
  });
  
  StreamingControllerState copyWith({
    bool? isSetupComplete,
    String? error,
    bool clearError = false,
    YouTubeLiveBroadcast? broadcast,
    YouTubeLiveStream? stream,
    String? chatId,
  }) {
    return StreamingControllerState(
      isSetupComplete: isSetupComplete ?? this.isSetupComplete,
      error: clearError ? null : (error ?? this.error),
      broadcast: broadcast ?? this.broadcast,
      stream: stream ?? this.stream,
      chatId: chatId ?? this.chatId,
    );
  }
}

/// State notifier for streaming controller
class StreamingControllerNotifier extends StateNotifier<StreamingControllerState> {
  final StreamingService _streamingService;
  final YouTubeChatService _chatService;
  final YouTubeApiService _apiService;
  
  StreamingControllerNotifier(
    this._streamingService,
    this._chatService,
    this._apiService,
  ) : super(const StreamingControllerState());
  
  /// Setup complete streaming pipeline
  Future<void> setupStreaming({
    required String title,
    required String description,
    StreamSettings? settings,
  }) async {
    try {
      state = state.copyWith(clearError: true);
      
      // 1. Create broadcast
      final broadcast = await _apiService.createLiveBroadcast(
        title: title,
        description: description,
      );
      
      state = state.copyWith(broadcast: broadcast);
      
      // 2. Create stream
      final stream = await _apiService.createLiveStream(
        title: '$title - Stream',
        settings: settings ?? const StreamSettings(),
      );
      
      state = state.copyWith(stream: stream);
      
      // 3. Bind stream to broadcast
      await _apiService.bindBroadcastToStream(broadcast.id, stream.id);
      
      // 4. Get chat ID
      final chatId = await _apiService.getLiveChatId(broadcast.id);
      
      state = state.copyWith(
        chatId: chatId,
        isSetupComplete: true,
      );
      
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }
  
  /// Start streaming
  Future<void> startStreaming() async {
    if (!state.isSetupComplete || state.stream == null) {
      throw Exception('Streaming not properly setup');
    }
    
    try {
      // Start RTMP streaming
      final rtmpUrl = state.stream!.ingestionAddress ?? '';
      final streamKey = state.stream!.streamName ?? '';
      
      await _streamingService.startStreaming(
        rtmpUrl: rtmpUrl,
        streamKey: streamKey,
      );
      
      // Start YouTube broadcast
      if (state.broadcast != null) {
        await _apiService.startLiveBroadcast(state.broadcast!.id);
      }
      
      // Start chat monitoring
      if (state.chatId != null) {
        await _chatService.startMonitoring(state.chatId!);
      }
      
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }
  
  /// Stop streaming
  Future<void> stopStreaming() async {
    try {
      // Stop RTMP streaming
      await _streamingService.stopStreaming();
      
      // Stop YouTube broadcast
      if (state.broadcast != null) {
        await _apiService.stopLiveBroadcast(state.broadcast!.id);
      }
      
      // Stop chat monitoring
      await _chatService.stopMonitoring();
      
      // Reset state
      state = const StreamingControllerState();
      
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }
}
