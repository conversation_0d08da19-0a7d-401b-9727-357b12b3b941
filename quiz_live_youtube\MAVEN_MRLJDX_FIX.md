# Fix pour FFmpeg Kit Maven (mrljdx)

## Problème Identifié

L'erreur indique que Gradle essaie de résoudre `com.arthenica:ffmpeg-kit-https:6.0-2` au lieu d'utiliser la version mrljdx `com.mrljdx:ffmpeg-kit-full:6.0`.

```
Could not find com.arthenica:ffmpeg-kit-https:6.0-2
```

## Solution Appliquée

### 1. Configuration Android (android/app/build.gradle.kts)

```kotlin
dependencies {
    // FFmpeg Kit for video processing and streaming - Version mrljdx
    implementation("com.mrljdx:ffmpeg-kit-full:6.0")

    // Additional dependencies for media handling
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.appcompat:appcompat:1.6.1")
}

// Configuration pour forcer l'utilisation de la version mrljdx
configurations.all {
    resolutionStrategy {
        // Forcer l'utilisation de la version mrljdx pour tous les modules FFmpeg Kit
        force("com.mrljdx:ffmpeg-kit-full:6.0")
        
        // Remplacer toutes les dépendances arthenica par celles de mrljdx
        dependencySubstitution {
            substitute(module("com.arthenica:ffmpeg-kit-android-full"))
                .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
            substitute(module("com.arthenica:ffmpeg-kit-https"))
                .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
            substitute(module("com.arthenica:ffmpeg-kit-min"))
                .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
            substitute(module("com.arthenica:ffmpeg-kit-audio"))
                .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
            substitute(module("com.arthenica:ffmpeg-kit-video"))
                .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
        }
    }
}
```

### 2. Configuration Flutter (pubspec.yaml)

```yaml
dependencies:
  # FFmpeg Kit - Compatible avec la version mrljdx
  ffmpeg_kit_flutter: 6.0.3

dependency_overrides:
  # Assurer la compatibilité avec la version mrljdx
  ffmpeg_kit_flutter: 6.0.3
```

## Étapes de Résolution

### 1. Nettoyage Complet

```bash
# Nettoyer Flutter
flutter clean
flutter pub cache clean

# Nettoyer Gradle
cd android
gradlew clean
gradlew --refresh-dependencies
cd ..

# Réinstaller les dépendances
flutter pub get
```

### 2. Exécution du Script de Fix

```bash
# Windows
fix_ffmpeg_maven.bat

# Ou manuellement
flutter clean
cd android && gradlew clean && cd ..
flutter pub get
flutter build apk --debug
```

### 3. Vérification

```bash
# Tester la configuration
dart verify_ffmpeg_maven.dart

# Ou build de test
flutter build apk --debug
```

## Vérification de la Configuration

### 1. Vérifier le Repository Maven

Le package mrljdx est disponible à :
- **URL**: https://repo1.maven.org/maven2/com/mrljdx/ffmpeg-kit-full/6.0/
- **Artifact**: `com.mrljdx:ffmpeg-kit-full:6.0`
- **Taille**: ~53MB (ffmpeg-kit-full-6.0.aar)

### 2. Vérifier la Substitution

La configuration `dependencySubstitution` remplace automatiquement :
- `com.arthenica:ffmpeg-kit-https:6.0-2` → `com.mrljdx:ffmpeg-kit-full:6.0`
- `com.arthenica:ffmpeg-kit-android-full` → `com.mrljdx:ffmpeg-kit-full:6.0`
- Tous les autres variants arthenica → mrljdx

### 3. Logs de Vérification

Pendant le build, vous devriez voir :
```
> Task :app:checkDebugAarMetadata
Resolving com.mrljdx:ffmpeg-kit-full:6.0
```

Au lieu de :
```
Could not find com.arthenica:ffmpeg-kit-https:6.0-2
```

## Dépannage Avancé

### 1. Si le Problème Persiste

```bash
# Vérifier les dépendances résolues
cd android
gradlew app:dependencies --configuration debugRuntimeClasspath | grep ffmpeg
```

### 2. Forcer la Résolution

Ajoutez dans `android/app/build.gradle.kts` :

```kotlin
configurations.all {
    resolutionStrategy {
        // Forcer explicitement
        force("com.mrljdx:ffmpeg-kit-full:6.0")
        
        // Exclure complètement arthenica
        exclude(group = "com.arthenica")
    }
}
```

### 3. Alternative Temporaire

Si le problème persiste, utilisez temporairement :

```yaml
# pubspec.yaml
dependencies:
  ffmpeg_kit_flutter: ^6.0.3  # Version officielle
```

Puis configurez uniquement Android pour mrljdx :

```kotlin
// android/app/build.gradle.kts
dependencies {
    implementation("com.mrljdx:ffmpeg-kit-full:6.0")
}
```

## Validation du Fix

### 1. Build Réussi

```bash
flutter build apk --debug
# Doit se terminer par : BUILD SUCCESSFUL
```

### 2. Test Runtime

```dart
// Test simple
final session = await FFmpegKit.execute('-version');
final returnCode = await session.getReturnCode();
print('FFmpeg Kit fonctionne: ${ReturnCode.isSuccess(returnCode)}');
```

### 3. Vérification des Logs

```dart
// Vérifier la version utilisée
final logs = await session.getAllLogs();
final versionInfo = logs.first.getMessage();
print('Version: $versionInfo');
```

## Avantages de la Version mrljdx

Une fois le fix appliqué, vous bénéficiez de :
- ✅ **Package mrljdx** : Version spécialisée
- ✅ **Maven Central** : Distribution fiable
- ✅ **Taille optimisée** : ~53MB pour la version full
- ✅ **Compatibilité** : Compatible avec l'API Flutter standard

## Résumé des Fichiers Modifiés

1. **android/app/build.gradle.kts** : Configuration de substitution des dépendances
2. **pubspec.yaml** : Version fixe et dependency_overrides
3. **Scripts ajoutés** :
   - `fix_ffmpeg_maven.bat` : Script de réparation automatique
   - `verify_ffmpeg_maven.dart` : Script de vérification
   - `MAVEN_MRLJDX_FIX.md` : Documentation du fix

## Support

Si le problème persiste après ces étapes :
1. Vérifiez la connectivité à Maven Central
2. Consultez les logs Gradle complets
3. Testez avec la version officielle temporairement
4. Vérifiez les versions de Gradle et Android Gradle Plugin

Le fix devrait résoudre définitivement l'erreur `Could not find com.arthenica:ffmpeg-kit-https:6.0-2` en forçant l'utilisation de `com.mrljdx:ffmpeg-kit-full:6.0`.
