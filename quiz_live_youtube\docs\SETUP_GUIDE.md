# Setup Guide for Quiz Live YouTube

This guide will walk you through setting up the Quiz Live YouTube application from scratch.

## Prerequisites

### 1. Development Environment

- **Flutter SDK**: Version 3.8.0 or higher
- **Android Studio**: Latest version with Android SDK
- **Git**: For version control
- **VS Code** (optional): With Flutter and Dart extensions

### 2. YouTube API Setup

#### Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Create Project" or select an existing project
3. Give your project a name (e.g., "Quiz Live YouTube")
4. Note down your Project ID

#### Step 2: Enable YouTube Data API

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "YouTube Data API v3"
3. Click on it and press "Enable"

#### Step 3: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client ID"
3. If prompted, configure the OAuth consent screen:
   - Choose "External" user type
   - Fill in required fields (App name, User support email, etc.)
   - Add your email to test users
4. For Application type, choose "Android"
5. Enter your package name: `com.example.quiz_live_youtube`
6. Get your SHA-1 certificate fingerprint:
   ```bash
   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
   ```
7. Enter the SHA-1 fingerprint
8. Click "Create"
9. Copy the Client ID

#### Step 4: Configure YouTube Live Streaming

1. Go to [YouTube Studio](https://studio.youtube.com/)
2. Click "Go Live" in the left sidebar
3. Enable live streaming (may require phone verification)
4. Note: New channels may have a 24-hour waiting period

## Installation Steps

### 1. Clone and Setup Project

```bash
# Clone the repository
git clone <repository-url>
cd quiz_live_youtube

# Install dependencies
flutter pub get

# Generate code files
flutter packages pub run build_runner build
```

### 2. Configure YouTube API

Edit `lib/core/services/youtube_auth_service.dart`:

```dart
static const String _clientId = 'YOUR_CLIENT_ID_HERE';
```

Replace `YOUR_CLIENT_ID_HERE` with the OAuth Client ID from Step 3 above.

### 3. Android Configuration

The Android configuration is already set up, but verify these files:

**android/app/src/main/AndroidManifest.xml**
- Permissions are correctly added
- Package name matches your OAuth configuration

**android/app/build.gradle.kts**
- Minimum SDK is set to 21
- FFmpeg Kit dependency is included

### 4. Test the Setup

```bash
# Run the app in debug mode
flutter run

# Run tests
flutter test

# Build release APK
flutter build apk --release
```

## Configuration Options

### Stream Quality Settings

Edit `lib/core/constants/app_constants.dart`:

```dart
// Stream Settings
static const int defaultStreamWidth = 1280;    // 720p, 1080p, etc.
static const int defaultStreamHeight = 720;
static const int defaultBitrate = 2500000;     // 2.5 Mbps
static const int defaultFrameRate = 30;        // 30 or 60 FPS
```

### Quiz Settings

```dart
// Quiz Settings
static const int defaultQuestionTimeLimit = 30; // seconds
static const int maxAnswerOptions = 4;
static const List<String> answerLabels = ['A', 'B', 'C', 'D'];
```

### Chat Settings

```dart
// Chat Settings
static const int chatPollInterval = 2000;      // milliseconds
static const int maxChatMessages = 100;
```

## Troubleshooting

### Common Issues

#### 1. "YouTube authentication failed"

**Cause**: Incorrect OAuth configuration
**Solution**: 
- Verify Client ID is correct
- Check package name matches OAuth configuration
- Ensure YouTube Data API v3 is enabled

#### 2. "Screen capture permission required"

**Cause**: Android MediaProjection permission not granted
**Solution**: 
- Grant permission when prompted
- Check that `SYSTEM_ALERT_WINDOW` permission is in manifest

#### 3. "Failed to start stream"

**Cause**: Various streaming issues
**Solution**: 
- Check internet connection
- Verify YouTube Live is enabled on your channel
- Ensure stream key is valid
- Check FFmpeg Kit installation

#### 4. Build errors with FFmpeg Kit

**Cause**: Native dependency issues
**Solution**: 
```bash
flutter clean
flutter pub get
cd android && ./gradlew clean
cd .. && flutter build apk
```

### Debug Mode

Enable detailed logging by adding to `main.dart`:

```dart
import 'package:logger/logger.dart';

void main() {
  Logger.level = Level.debug;
  runApp(const ProviderScope(child: QuizLiveYouTubeApp()));
}
```

### Testing Without YouTube

For development without YouTube API:
1. Comment out YouTube authentication in splash screen
2. Use mock data for testing UI components
3. Test streaming with local RTMP server

## Security Considerations

### API Keys
- Never commit API keys to version control
- Use environment variables for production
- Implement proper key rotation

### Permissions
- Request permissions only when needed
- Explain permission usage to users
- Handle permission denials gracefully

### Streaming
- Validate stream keys before use
- Implement rate limiting for API calls
- Monitor for abuse and unusual activity

## Production Deployment

### 1. Prepare for Release

```bash
# Update version in pubspec.yaml
version: 1.0.0+1

# Build release APK
flutter build apk --release

# Build App Bundle for Google Play
flutter build appbundle --release
```

### 2. Google Play Store

1. Create developer account
2. Upload App Bundle
3. Fill in store listing details
4. Set up content rating
5. Configure pricing and distribution

### 3. Testing

- Test on multiple devices
- Verify all permissions work
- Test streaming with real YouTube account
- Validate chat integration

## Support

For additional help:
- Check Flutter documentation: https://flutter.dev/docs
- YouTube API documentation: https://developers.google.com/youtube/v3
- FFmpeg Kit documentation: https://github.com/arthenica/ffmpeg-kit

## Next Steps

After setup:
1. Customize the app theme and branding
2. Add more quiz question types
3. Implement analytics and monitoring
4. Add user feedback and rating system
5. Consider iOS support (requires additional setup)
