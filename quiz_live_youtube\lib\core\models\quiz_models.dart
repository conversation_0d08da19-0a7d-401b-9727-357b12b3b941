/// Represents a quiz question with multiple choice answers
class QuizQuestion {
  final String id;
  final String title;
  final String questionText;
  final List<String> options;
  final int correctAnswerIndex;
  final int timeLimit;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const QuizQuestion({
    required this.id,
    required this.title,
    required this.questionText,
    required this.options,
    required this.correctAnswerIndex,
    this.timeLimit = 30,
    this.createdAt,
    this.updatedAt,
  });

  QuizQuestion copyWith({
    String? id,
    String? title,
    String? questionText,
    List<String>? options,
    int? correctAnswerIndex,
    int? timeLimit,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return QuizQuestion(
      id: id ?? this.id,
      title: title ?? this.title,
      questionText: questionText ?? this.questionText,
      options: options ?? this.options,
      correctAnswerIndex: correctAnswerIndex ?? this.correctAnswerIndex,
      timeLimit: timeLimit ?? this.timeLimit,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'questionText': questionText,
      'options': options,
      'correctAnswerIndex': correctAnswerIndex,
      'timeLimit': timeLimit,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    return QuizQuestion(
      id: json['id'] as String,
      title: json['title'] as String,
      questionText: json['questionText'] as String,
      options: List<String>.from(json['options'] as List),
      correctAnswerIndex: json['correctAnswerIndex'] as int,
      timeLimit: json['timeLimit'] as int? ?? 30,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt'] as String) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt'] as String) : null,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuizQuestion &&
        other.id == id &&
        other.title == title &&
        other.questionText == questionText &&
        other.options.toString() == options.toString() &&
        other.correctAnswerIndex == correctAnswerIndex &&
        other.timeLimit == timeLimit;
  }

  @override
  int get hashCode {
    return Object.hash(id, title, questionText, options, correctAnswerIndex, timeLimit);
  }

  @override
  String toString() {
    return 'QuizQuestion(id: $id, title: $title, questionText: $questionText, options: $options, correctAnswerIndex: $correctAnswerIndex, timeLimit: $timeLimit)';
  }
}

/// Represents a complete quiz session
class Quiz {
  final String id;
  final String title;
  final String description;
  final List<QuizQuestion> questions;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Quiz({
    required this.id,
    required this.title,
    required this.description,
    required this.questions,
    this.isActive = false,
    this.createdAt,
    this.updatedAt,
  });

  Quiz copyWith({
    String? id,
    String? title,
    String? description,
    List<QuizQuestion>? questions,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Quiz(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      questions: questions ?? this.questions,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'questions': questions.map((q) => q.toJson()).toList(),
      'isActive': isActive,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory Quiz.fromJson(Map<String, dynamic> json) {
    return Quiz(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      questions: (json['questions'] as List)
          .map((q) => QuizQuestion.fromJson(q as Map<String, dynamic>))
          .toList(),
      isActive: json['isActive'] as bool? ?? false,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt'] as String) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt'] as String) : null,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Quiz &&
        other.id == id &&
        other.title == title &&
        other.description == description &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return Object.hash(id, title, description, isActive);
  }

  @override
  String toString() {
    return 'Quiz(id: $id, title: $title, description: $description, questions: ${questions.length}, isActive: $isActive)';
  }
}

/// Represents a participant's answer
class QuizAnswer {
  final String id;
  final String questionId;
  final String participantName;
  final String participantId;
  final int selectedOption;
  final DateTime timestamp;
  final bool isCorrect;

  const QuizAnswer({
    required this.id,
    required this.questionId,
    required this.participantName,
    required this.participantId,
    required this.selectedOption,
    required this.timestamp,
    this.isCorrect = false,
  });

  QuizAnswer copyWith({
    String? id,
    String? questionId,
    String? participantName,
    String? participantId,
    int? selectedOption,
    DateTime? timestamp,
    bool? isCorrect,
  }) {
    return QuizAnswer(
      id: id ?? this.id,
      questionId: questionId ?? this.questionId,
      participantName: participantName ?? this.participantName,
      participantId: participantId ?? this.participantId,
      selectedOption: selectedOption ?? this.selectedOption,
      timestamp: timestamp ?? this.timestamp,
      isCorrect: isCorrect ?? this.isCorrect,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'questionId': questionId,
      'participantName': participantName,
      'participantId': participantId,
      'selectedOption': selectedOption,
      'timestamp': timestamp.toIso8601String(),
      'isCorrect': isCorrect,
    };
  }

  factory QuizAnswer.fromJson(Map<String, dynamic> json) {
    return QuizAnswer(
      id: json['id'] as String,
      questionId: json['questionId'] as String,
      participantName: json['participantName'] as String,
      participantId: json['participantId'] as String,
      selectedOption: json['selectedOption'] as int,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isCorrect: json['isCorrect'] as bool? ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuizAnswer &&
        other.id == id &&
        other.questionId == questionId &&
        other.participantId == participantId &&
        other.selectedOption == selectedOption;
  }

  @override
  int get hashCode {
    return Object.hash(id, questionId, participantId, selectedOption);
  }

  @override
  String toString() {
    return 'QuizAnswer(id: $id, participantName: $participantName, selectedOption: $selectedOption, isCorrect: $isCorrect)';
  }
}

/// Represents a quiz session result
class QuizResult {
  final String id;
  final String quizId;
  final String questionId;
  final List<QuizAnswer> answers;
  final QuizAnswer? winner;
  final List<QuizAnswer> topAnswers;
  final DateTime? completedAt;

  const QuizResult({
    required this.id,
    required this.quizId,
    required this.questionId,
    required this.answers,
    this.winner,
    this.topAnswers = const [],
    this.completedAt,
  });

  QuizResult copyWith({
    String? id,
    String? quizId,
    String? questionId,
    List<QuizAnswer>? answers,
    QuizAnswer? winner,
    List<QuizAnswer>? topAnswers,
    DateTime? completedAt,
  }) {
    return QuizResult(
      id: id ?? this.id,
      quizId: quizId ?? this.quizId,
      questionId: questionId ?? this.questionId,
      answers: answers ?? this.answers,
      winner: winner ?? this.winner,
      topAnswers: topAnswers ?? this.topAnswers,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quizId': quizId,
      'questionId': questionId,
      'answers': answers.map((a) => a.toJson()).toList(),
      'winner': winner?.toJson(),
      'topAnswers': topAnswers.map((a) => a.toJson()).toList(),
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  factory QuizResult.fromJson(Map<String, dynamic> json) {
    return QuizResult(
      id: json['id'] as String,
      quizId: json['quizId'] as String,
      questionId: json['questionId'] as String,
      answers: (json['answers'] as List)
          .map((a) => QuizAnswer.fromJson(a as Map<String, dynamic>))
          .toList(),
      winner: json['winner'] != null
          ? QuizAnswer.fromJson(json['winner'] as Map<String, dynamic>)
          : null,
      topAnswers: (json['topAnswers'] as List? ?? [])
          .map((a) => QuizAnswer.fromJson(a as Map<String, dynamic>))
          .toList(),
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
    );
  }
}

/// Represents the current state of a live quiz session
class LiveQuizSession {
  final String id;
  final Quiz quiz;
  final int currentQuestionIndex;
  final List<QuizResult> results;
  final QuizSessionStatus status;
  final DateTime? startedAt;
  final DateTime? currentQuestionStartedAt;

  const LiveQuizSession({
    required this.id,
    required this.quiz,
    this.currentQuestionIndex = 0,
    this.results = const [],
    this.status = QuizSessionStatus.waiting,
    this.startedAt,
    this.currentQuestionStartedAt,
  });

  LiveQuizSession copyWith({
    String? id,
    Quiz? quiz,
    int? currentQuestionIndex,
    List<QuizResult>? results,
    QuizSessionStatus? status,
    DateTime? startedAt,
    DateTime? currentQuestionStartedAt,
  }) {
    return LiveQuizSession(
      id: id ?? this.id,
      quiz: quiz ?? this.quiz,
      currentQuestionIndex: currentQuestionIndex ?? this.currentQuestionIndex,
      results: results ?? this.results,
      status: status ?? this.status,
      startedAt: startedAt ?? this.startedAt,
      currentQuestionStartedAt: currentQuestionStartedAt ?? this.currentQuestionStartedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quiz': quiz.toJson(),
      'currentQuestionIndex': currentQuestionIndex,
      'results': results.map((r) => r.toJson()).toList(),
      'status': status.name,
      'startedAt': startedAt?.toIso8601String(),
      'currentQuestionStartedAt': currentQuestionStartedAt?.toIso8601String(),
    };
  }

  factory LiveQuizSession.fromJson(Map<String, dynamic> json) {
    return LiveQuizSession(
      id: json['id'] as String,
      quiz: Quiz.fromJson(json['quiz'] as Map<String, dynamic>),
      currentQuestionIndex: json['currentQuestionIndex'] as int? ?? 0,
      results: (json['results'] as List? ?? [])
          .map((r) => QuizResult.fromJson(r as Map<String, dynamic>))
          .toList(),
      status: QuizSessionStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => QuizSessionStatus.waiting,
      ),
      startedAt: json['startedAt'] != null
          ? DateTime.parse(json['startedAt'] as String)
          : null,
      currentQuestionStartedAt: json['currentQuestionStartedAt'] != null
          ? DateTime.parse(json['currentQuestionStartedAt'] as String)
          : null,
    );
  }
}

/// Status of a quiz session
enum QuizSessionStatus {
  waiting,
  active,
  questionActive,
  questionCompleted,
  completed,
  paused,
}

/// Represents leaderboard entry
class LeaderboardEntry {
  final String participantName;
  final String participantId;
  final int correctAnswers;
  final int totalAnswers;
  final double averageResponseTime;
  final int points;

  const LeaderboardEntry({
    required this.participantName,
    required this.participantId,
    this.correctAnswers = 0,
    this.totalAnswers = 0,
    this.averageResponseTime = 0.0,
    this.points = 0,
  });

  LeaderboardEntry copyWith({
    String? participantName,
    String? participantId,
    int? correctAnswers,
    int? totalAnswers,
    double? averageResponseTime,
    int? points,
  }) {
    return LeaderboardEntry(
      participantName: participantName ?? this.participantName,
      participantId: participantId ?? this.participantId,
      correctAnswers: correctAnswers ?? this.correctAnswers,
      totalAnswers: totalAnswers ?? this.totalAnswers,
      averageResponseTime: averageResponseTime ?? this.averageResponseTime,
      points: points ?? this.points,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'participantName': participantName,
      'participantId': participantId,
      'correctAnswers': correctAnswers,
      'totalAnswers': totalAnswers,
      'averageResponseTime': averageResponseTime,
      'points': points,
    };
  }

  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) {
    return LeaderboardEntry(
      participantName: json['participantName'] as String,
      participantId: json['participantId'] as String,
      correctAnswers: json['correctAnswers'] as int? ?? 0,
      totalAnswers: json['totalAnswers'] as int? ?? 0,
      averageResponseTime: (json['averageResponseTime'] as num?)?.toDouble() ?? 0.0,
      points: json['points'] as int? ?? 0,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LeaderboardEntry &&
        other.participantId == participantId &&
        other.participantName == participantName;
  }

  @override
  int get hashCode {
    return Object.hash(participantId, participantName);
  }

  @override
  String toString() {
    return 'LeaderboardEntry(participantName: $participantName, points: $points, correctAnswers: $correctAnswers/$totalAnswers)';
  }
}
