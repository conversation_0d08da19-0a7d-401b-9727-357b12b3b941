import 'package:flutter/material.dart';
import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter/return_code.dart';
import 'package:ffmpeg_kit_flutter/ffmpeg_kit_config.dart';

/// Script de vérification pour confirmer l'utilisation de la version mrljdx
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🔍 Vérification de la version FFmpeg Kit (mrljdx)');
  print('==================================================');
  
  try {
    await verifyFFmpegVersion();
    await verifyFFmpegCapabilities();
    await testBasicCommand();
    
    print('\n✅ Vérification terminée avec succès !');
    print('La version mrljdx de FFmpeg Kit est correctement installée.');
    
  } catch (e) {
    print('\n❌ Erreur lors de la vérification: $e');
  }
}

/// Vérifier la version FFmpeg
Future<void> verifyFFmpegVersion() async {
  print('\n📋 Test 1: Vérification de la version FFmpeg...');
  
  try {
    final session = await FFmpegKit.execute('-version');
    final returnCode = await session.getReturnCode();
    
    if (ReturnCode.isSuccess(returnCode)) {
      print('   ✅ FFmpeg accessible');
      
      final logs = await session.getAllLogs();
      if (logs.isNotEmpty) {
        final versionInfo = logs.first.getMessage();
        final lines = versionInfo.split('\n');
        
        // Afficher les informations de version
        for (final line in lines.take(5)) {
          if (line.trim().isNotEmpty) {
            print('   📄 $line');
          }
        }
        
        // Vérifier si c'est la version mrljdx (rechercher des indices)
        if (versionInfo.contains('mrljdx') || versionInfo.contains('6.0')) {
          print('   🎯 Version mrljdx détectée !');
        } else {
          print('   ⚠️  Version standard détectée (pas mrljdx)');
        }
      }
    } else {
      print('   ❌ Échec de l\'accès à FFmpeg');
    }
  } catch (e) {
    print('   ❌ Erreur: $e');
    rethrow;
  }
}

/// Vérifier les capacités FFmpeg
Future<void> verifyFFmpegCapabilities() async {
  print('\n📋 Test 2: Vérification des capacités FFmpeg...');
  
  try {
    // Tester les codecs disponibles
    final codecSession = await FFmpegKit.execute('-codecs');
    final codecReturnCode = await codecSession.getReturnCode();
    
    if (ReturnCode.isSuccess(codecReturnCode)) {
      print('   ✅ Codecs accessibles');
      
      final logs = await codecSession.getAllLogs();
      final codecList = <String>[];
      
      for (final log in logs) {
        final message = log.getMessage();
        if (message.contains('DEV')) {
          // Extraire le nom du codec
          final parts = message.trim().split(RegExp(r'\s+'));
          if (parts.length > 1) {
            codecList.add(parts[1]);
          }
        }
      }
      
      print('   📊 Nombre de codecs: ${codecList.length}');
      
      // Vérifier des codecs importants
      final importantCodecs = ['libx264', 'aac', 'h264', 'mp3'];
      final availableImportant = <String>[];
      
      for (final codec in importantCodecs) {
        if (codecList.any((c) => c.toLowerCase().contains(codec.toLowerCase()))) {
          availableImportant.add(codec);
        }
      }
      
      print('   🎯 Codecs importants disponibles: ${availableImportant.join(', ')}');
      
      if (availableImportant.length >= 2) {
        print('   ✅ Capacités suffisantes pour le streaming');
      } else {
        print('   ⚠️  Capacités limitées');
      }
    } else {
      print('   ❌ Impossible d\'accéder aux codecs');
    }
  } catch (e) {
    print('   ❌ Erreur: $e');
  }
}

/// Tester une commande basique
Future<void> testBasicCommand() async {
  print('\n📋 Test 3: Test de commande basique...');
  
  try {
    // Tester une commande simple qui génère une vidéo de test
    final command = [
      '-f', 'lavfi',
      '-i', 'testsrc=size=320x240:rate=1:duration=1',
      '-f', 'null',
      '-'
    ].join(' ');
    
    print('   🔧 Commande: $command');
    
    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();
    
    if (ReturnCode.isSuccess(returnCode)) {
      print('   ✅ Commande exécutée avec succès');
      
      // Vérifier les statistiques
      final statistics = await session.getAllStatistics();
      if (statistics.isNotEmpty) {
        final lastStat = statistics.last;
        print('   📊 Frames traités: ${lastStat.getVideoFrameNumber()}');
        print('   ⏱️  Temps: ${lastStat.getTime()}ms');
      }
    } else {
      print('   ❌ Échec de la commande');
      
      // Afficher les erreurs
      final logs = await session.getAllLogs();
      for (final log in logs.take(3)) {
        if (log.getMessage().toLowerCase().contains('error')) {
          print('   🔍 Erreur: ${log.getMessage()}');
        }
      }
    }
  } catch (e) {
    print('   ❌ Erreur: $e');
  }
}

/// Vérifier la configuration FFmpeg Kit
Future<void> verifyFFmpegKitConfig() async {
  print('\n📋 Test 4: Vérification de la configuration FFmpeg Kit...');
  
  try {
    // Obtenir des informations sur la configuration
    final version = await FFmpegKitConfig.getVersion();
    print('   📄 Version FFmpeg Kit: $version');
    
    // Vérifier les logs
    print('   🔧 Configuration des logs...');
    FFmpegKitConfig.enableLogCallback((log) {
      // Callback configuré
    });
    
    print('   ✅ Configuration FFmpeg Kit OK');
  } catch (e) {
    print('   ❌ Erreur de configuration: $e');
  }
}

/// Tester la génération de commande RTMP
void testRTMPCommandGeneration() {
  print('\n📋 Test 5: Génération de commande RTMP...');
  
  try {
    final rtmpUrl = 'rtmp://test-server.com/live/test-key';
    final command = buildRTMPCommand(rtmpUrl);
    
    print('   ✅ Commande RTMP générée');
    print('   📝 Commande: $command');
    
    // Vérifier les éléments essentiels
    final requiredElements = [
      '-f', 'lavfi',
      '-i', 'testsrc',
      '-c:v', 'libx264',
      '-f', 'flv',
      rtmpUrl
    ];
    
    bool allPresent = true;
    for (final element in requiredElements) {
      if (!command.contains(element)) {
        print('   ⚠️  Élément manquant: $element');
        allPresent = false;
      }
    }
    
    if (allPresent) {
      print('   ✅ Tous les éléments RTMP présents');
    } else {
      print('   ❌ Éléments RTMP manquants');
    }
  } catch (e) {
    print('   ❌ Erreur: $e');
  }
}

/// Construire une commande RTMP de test
String buildRTMPCommand(String rtmpUrl) {
  final args = [
    // Source de test
    '-f', 'lavfi',
    '-i', 'testsrc=size=1280x720:rate=30',
    
    // Encodage vidéo
    '-c:v', 'libx264',
    '-preset', 'ultrafast',
    '-tune', 'zerolatency',
    '-b:v', '2500k',
    '-maxrate', '2500k',
    '-bufsize', '5000k',
    
    // Audio de test
    '-f', 'lavfi',
    '-i', 'anullsrc=channel_layout=stereo:sample_rate=44100',
    '-c:a', 'aac',
    '-b:a', '128k',
    
    // Sortie RTMP
    '-f', 'flv',
    rtmpUrl,
  ];
  
  return args.join(' ');
}

/// Fonction principale pour exécuter tous les tests
Future<void> runCompleteVerification() async {
  print('🚀 Vérification complète FFmpeg Kit (mrljdx)');
  print('=============================================');
  
  final tests = <String, Future<void> Function()>{
    'Version FFmpeg': verifyFFmpegVersion,
    'Capacités FFmpeg': verifyFFmpegCapabilities,
    'Commande basique': testBasicCommand,
    'Configuration FFmpeg Kit': verifyFFmpegKitConfig,
  };
  
  final results = <String, bool>{};
  
  for (final entry in tests.entries) {
    try {
      await entry.value();
      results[entry.key] = true;
    } catch (e) {
      results[entry.key] = false;
      print('❌ Échec du test "${entry.key}": $e');
    }
  }
  
  // Test synchrone
  try {
    testRTMPCommandGeneration();
    results['Génération RTMP'] = true;
  } catch (e) {
    results['Génération RTMP'] = false;
  }
  
  // Résumé
  print('\n📊 Résumé de la Vérification');
  print('============================');
  
  int passed = 0;
  for (final entry in results.entries) {
    final status = entry.value ? '✅' : '❌';
    print('$status ${entry.key}');
    if (entry.value) passed++;
  }
  
  print('\n📈 Résultat: $passed/${results.length} tests réussis');
  
  if (passed == results.length) {
    print('🎉 Tous les tests sont passés !');
    print('🎯 La version mrljdx de FFmpeg Kit est prête à être utilisée.');
  } else {
    print('⚠️  Certains tests ont échoué.');
    print('🔍 Vérifiez la configuration et consultez les logs d\'erreur.');
  }
}
