@echo off
echo 🔧 Test de la syntaxe Gradle
echo ============================

echo.
echo 📋 Test de compilation du build.gradle.kts...
echo ----------------------------------------

cd android
if %ERRORLEVEL% neq 0 (
    echo ❌ Dossier android non trouvé
    pause
    exit /b 1
)

echo Test de la syntaxe Gradle...
gradlew help --dry-run
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de syntaxe dans build.gradle.kts
    echo.
    echo 🔍 Vérifiez les erreurs ci-dessus
    echo 📄 Fichier concerné: android/build.gradle.kts
    echo.
    cd ..
    pause
    exit /b 1
)

echo ✅ Syntaxe Gradle correcte !

echo.
echo 📋 Test de résolution des dépendances...
echo ----------------------------------------

echo Vérification des dépendances...
gradlew app:dependencies --configuration debugCompileClasspath --dry-run
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors de la résolution des dépendances
    cd ..
    pause
    exit /b 1
)

echo ✅ Résolution des dépendances OK !

cd ..

echo.
echo 📋 Test Flutter pub get...
echo ----------------------------------------

flutter pub get
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur Flutter pub get
    pause
    exit /b 1
)

echo ✅ Flutter pub get réussi !

echo.
echo 🎉 Tous les tests de syntaxe sont passés !
echo ==========================================
echo.
echo 🎯 Prochaine étape: flutter build apk --debug
echo.

pause
