/// Application-wide constants
class AppConstants {
  // App Information
  static const String appName = 'Quiz Live YouTube';
  static const String appVersion = '1.0.0';
  
  // YouTube API
  static const String youtubeApiBaseUrl = 'https://www.googleapis.com/youtube/v3';
  static const String youtubeLiveChatBaseUrl = 'https://www.googleapis.com/youtube/v3/liveChat';
  static const String youtubeRtmpUrl = 'rtmp://a.rtmp.youtube.com/live2';
  
  // OAuth Scopes
  static const List<String> youtubeScopes = [
    'https://www.googleapis.com/auth/youtube',
    'https://www.googleapis.com/auth/youtube.force-ssl',
    'https://www.googleapis.com/auth/youtube.readonly',
  ];
  
  // Stream Settings
  static const int defaultStreamWidth = 1280;
  static const int defaultStreamHeight = 720;
  static const int defaultBitrate = 2500000; // 2.5 Mbps
  static const int defaultFrameRate = 30;
  
  // Quiz Settings
  static const int defaultQuestionTimeLimit = 30; // seconds
  static const int maxAnswerOptions = 4;
  static const List<String> answerLabels = ['A', 'B', 'C', 'D'];
  
  // Chat Settings
  static const int chatPollInterval = 2000; // milliseconds
  static const int maxChatMessages = 100;
  
  // Database
  static const String databaseName = 'quiz_live.db';
  static const int databaseVersion = 1;
  
  // Storage Keys
  static const String keyYoutubeToken = 'youtube_token';
  static const String keyStreamKey = 'stream_key';
  static const String keyUserPreferences = 'user_preferences';
  static const String keyThemeMode = 'theme_mode';
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 800);
  
  // Error Messages
  static const String errorNetworkConnection = 'No internet connection';
  static const String errorYoutubeAuth = 'YouTube authentication failed';
  static const String errorStreamStart = 'Failed to start stream';
  static const String errorChatConnection = 'Failed to connect to chat';
  static const String errorQuizLoad = 'Failed to load quiz';
}
