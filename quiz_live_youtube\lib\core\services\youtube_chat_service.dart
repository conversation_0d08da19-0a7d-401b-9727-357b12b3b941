import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';

import '../constants/app_constants.dart';
import '../models/youtube_models.dart';
import 'youtube_auth_service.dart';

/// Service for monitoring YouTube Live Chat
class YouTubeChatService {
  static final Logger _logger = Logger();
  final YouTubeAuthService _authService;
  
  Timer? _chatTimer;
  String? _liveChatId;
  String? _nextPageToken;
  bool _isMonitoring = false;
  
  final StreamController<List<YouTubeChatMessage>> _chatController = 
      StreamController<List<YouTubeChatMessage>>.broadcast();
  
  YouTubeChatService(this._authService);
  
  /// Stream of chat messages
  Stream<List<YouTubeChatMessage>> get chatMessages => _chatController.stream;
  
  /// Check if currently monitoring chat
  bool get isMonitoring => _isMonitoring;
  
  /// Start monitoring live chat
  Future<void> startMonitoring(String liveChatId) async {
    if (_isMonitoring) {
      await stopMonitoring();
    }
    
    _liveChatId = liveChatId;
    _nextPageToken = null;
    _isMonitoring = true;
    
    _logger.i('Starting chat monitoring for chat ID: $liveChatId');
    
    // Start polling for messages
    _chatTimer = Timer.periodic(
      Duration(milliseconds: AppConstants.chatPollInterval),
      (_) => _fetchChatMessages(),
    );
    
    // Fetch initial messages
    await _fetchChatMessages();
  }
  
  /// Stop monitoring live chat
  Future<void> stopMonitoring() async {
    _isMonitoring = false;
    _chatTimer?.cancel();
    _chatTimer = null;
    _liveChatId = null;
    _nextPageToken = null;
    
    _logger.i('Stopped chat monitoring');
  }
  
  /// Fetch chat messages from YouTube API
  Future<void> _fetchChatMessages() async {
    if (!_isMonitoring || _liveChatId == null) return;
    
    try {
      final uri = Uri.parse(
        '${AppConstants.youtubeLiveChatBaseUrl}/messages'
        '?liveChatId=$_liveChatId'
        '&part=snippet,authorDetails'
        '&maxResults=200'
        '${_nextPageToken != null ? '&pageToken=$_nextPageToken' : ''}'
      );
      
      final response = await http.get(
        uri,
        headers: _authService.getAuthHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _nextPageToken = data['nextPageToken'];
        
        final items = data['items'] as List? ?? [];
        final messages = items.map((item) => _parseMessage(item)).toList();
        
        if (messages.isNotEmpty) {
          _chatController.add(messages);
          _logger.d('Fetched ${messages.length} chat messages');
        }
        
        // Update polling interval based on API response
        final pollingInterval = data['pollingIntervalMillis'] as int?;
        if (pollingInterval != null && pollingInterval != AppConstants.chatPollInterval) {
          _updatePollingInterval(pollingInterval);
        }
      } else if (response.statusCode == 403) {
        _logger.w('Chat access forbidden - may be disabled or private');
        await stopMonitoring();
      } else if (response.statusCode == 401) {
        _logger.e('Authentication failed while fetching chat messages');
        await stopMonitoring();
      } else {
        _logger.w('Failed to fetch chat messages: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      _logger.e('Error fetching chat messages: $e');
    }
  }
  
  /// Parse a chat message from API response
  YouTubeChatMessage _parseMessage(Map<String, dynamic> item) {
    final snippet = item['snippet'];
    final authorDetails = item['authorDetails'];
    
    return YouTubeChatMessage(
      id: item['id'],
      authorChannelId: authorDetails['channelId'] ?? '',
      authorDisplayName: authorDetails['displayName'] ?? 'Unknown',
      messageText: snippet['displayMessage'] ?? '',
      publishedAt: DateTime.parse(snippet['publishedAt']),
      isFromOwner: authorDetails['isChatOwner'] ?? false,
      isModerator: authorDetails['isChatModerator'] ?? false,
      isSponsor: authorDetails['isChatSponsor'] ?? false,
    );
  }
  
  /// Update polling interval
  void _updatePollingInterval(int intervalMs) {
    _chatTimer?.cancel();
    _chatTimer = Timer.periodic(
      Duration(milliseconds: intervalMs),
      (_) => _fetchChatMessages(),
    );
    _logger.d('Updated polling interval to ${intervalMs}ms');
  }
  
  /// Send a message to the live chat
  Future<void> sendMessage(String message) async {
    if (_liveChatId == null) {
      throw Exception('No active live chat');
    }
    
    try {
      final messageData = {
        'snippet': {
          'liveChatId': _liveChatId,
          'type': 'textMessageEvent',
          'textMessageDetails': {
            'messageText': message,
          },
        },
      };
      
      final response = await http.post(
        Uri.parse('${AppConstants.youtubeLiveChatBaseUrl}/messages?part=snippet'),
        headers: _authService.getAuthHeaders(),
        body: json.encode(messageData),
      );
      
      if (response.statusCode == 200) {
        _logger.i('Message sent successfully');
      } else {
        throw Exception('Failed to send message: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Failed to send chat message: $e');
      rethrow;
    }
  }
  
  /// Filter messages for quiz answers
  List<YouTubeChatMessage> filterAnswerMessages(
    List<YouTubeChatMessage> messages,
    List<String> validAnswers,
  ) {
    return messages.where((message) {
      final text = message.messageText.trim().toUpperCase();
      return validAnswers.any((answer) => text == answer.toUpperCase());
    }).toList();
  }
  
  /// Find the first correct answer from messages
  YouTubeChatMessage? findFirstCorrectAnswer(
    List<YouTubeChatMessage> messages,
    String correctAnswer,
  ) {
    final correctAnswerUpper = correctAnswer.toUpperCase();
    
    for (final message in messages) {
      final text = message.messageText.trim().toUpperCase();
      if (text == correctAnswerUpper) {
        return message;
      }
    }
    
    return null;
  }
  
  /// Get top N fastest correct answers
  List<YouTubeChatMessage> getTopCorrectAnswers(
    List<YouTubeChatMessage> messages,
    String correctAnswer,
    int count,
  ) {
    final correctAnswerUpper = correctAnswer.toUpperCase();
    
    final correctMessages = messages
        .where((message) => 
            message.messageText.trim().toUpperCase() == correctAnswerUpper)
        .toList();
    
    // Sort by timestamp (earliest first)
    correctMessages.sort((a, b) => a.publishedAt.compareTo(b.publishedAt));
    
    return correctMessages.take(count).toList();
  }
  
  /// Dispose resources
  void dispose() {
    stopMonitoring();
    _chatController.close();
  }
}
