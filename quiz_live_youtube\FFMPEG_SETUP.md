# Configuration FFmpeg Kit

## Installation

Le projet utilise maintenant le package `ffmpeg-kit` personnalisé de GitHub au lieu de `ffmpeg-kit-full`.

### 1. Dépendance dans pubspec.yaml

```yaml
dependencies:
  ffmpeg_kit_flutter:
    git:
      url: https://github.com/mrljdx/ffmpeg-kit.git
      path: flutter/flutter
```

### 2. Installation des Dépendances

```bash
flutter pub get
```

## Utilisation

### Service de Streaming Basique

```dart
import 'package:quiz_live_youtube/core/services/streaming_service.dart';

// Initialiser le service
final streamingService = StreamingService();
await streamingService.initialize();

// Configurer les paramètres de stream
final settings = StreamSettings(
  width: 1280,
  height: 720,
  frameRate: 30,
  bitrate: 2500,
  codec: 'libx264',
);

// Démarrer le streaming
await streamingService.startStreaming(
  rtmpUrl: 'rtmp://your-server.com/live/stream-key',
  streamSettings: settings,
);

// Arrêter le streaming
await streamingService.stopStreaming();
```

### Commandes FFmpeg Personnalisées

Le service construit automatiquement les commandes FFmpeg optimisées :

```dart
String _buildFFmpegCommand(String rtmpUrl, StreamSettings settings) {
  final List<String> args = [
    // Source d'entrée (actuellement source de test)
    '-f', 'lavfi',
    '-i', 'testsrc=size=${settings.width}x${settings.height}:rate=${settings.frameRate}',
    
    // Encodage vidéo optimisé pour le streaming
    '-c:v', settings.codec,
    '-b:v', '${settings.bitrate}',
    '-preset', 'ultrafast',
    '-tune', 'zerolatency',
    
    // Sortie RTMP
    '-f', 'flv',
    rtmpUrl,
  ];
  
  return args.join(' ');
}
```

## Capture d'Écran (À Implémenter)

### Android - MediaProjection

Pour capturer l'écran sur Android, vous devrez implémenter :

```kotlin
// android/app/src/main/kotlin/MainActivity.kt
class MainActivity: FlutterActivity() {
    private val SCREEN_CAPTURE_REQUEST = 1000
    private var mediaProjectionManager: MediaProjectionManager? = null
    
    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "quiz_live_youtube/screen_capture")
            .setMethodCallHandler { call, result ->
                when (call.method) {
                    "requestPermission" -> requestScreenCapturePermission(result)
                    "startCapture" -> startScreenCapture(call.arguments, result)
                    "stopCapture" -> stopScreenCapture(result)
                    else -> result.notImplemented()
                }
            }
    }
    
    private fun requestScreenCapturePermission(result: MethodChannel.Result) {
        mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        val intent = mediaProjectionManager?.createScreenCaptureIntent()
        startActivityForResult(intent, SCREEN_CAPTURE_REQUEST)
    }
}
```

### iOS - ReplayKit

Pour iOS, utilisez ReplayKit :

```swift
// ios/Runner/AppDelegate.swift
import ReplayKit

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        
        let controller = window?.rootViewController as! FlutterViewController
        let screenCaptureChannel = FlutterMethodChannel(
            name: "quiz_live_youtube/screen_capture",
            binaryMessenger: controller.binaryMessenger
        )
        
        screenCaptureChannel.setMethodCallHandler { call, result in
            switch call.method {
            case "requestPermission":
                self.requestScreenRecordingPermission(result: result)
            case "startCapture":
                self.startScreenCapture(arguments: call.arguments, result: result)
            case "stopCapture":
                self.stopScreenCapture(result: result)
            default:
                result(FlutterMethodNotImplemented)
            }
        }
        
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    private func requestScreenRecordingPermission(result: @escaping FlutterResult) {
        RPScreenRecorder.shared().requestRecordingPermission { granted in
            result(granted)
        }
    }
}
```

## Optimisations FFmpeg

### Paramètres pour Streaming en Direct

```dart
final optimizedArgs = [
  // Paramètres d'entrée
  '-f', 'rawvideo',
  '-pixel_format', 'rgba',
  '-video_size', '${width}x$height',
  '-framerate', '$frameRate',
  '-i', 'pipe:0',
  
  // Encodage optimisé
  '-c:v', 'libx264',
  '-preset', 'ultrafast',      // Vitesse maximale
  '-tune', 'zerolatency',      // Latence minimale
  '-profile:v', 'baseline',    // Compatibilité maximale
  
  // Contrôle du débit
  '-b:v', '${bitrate}k',
  '-maxrate', '${bitrate}k',
  '-bufsize', '${bitrate * 2}k',
  
  // GOP et keyframes
  '-g', '$frameRate',          // GOP = framerate
  '-keyint_min', '$frameRate', // Keyframe minimum
  '-sc_threshold', '0',        // Désactiver détection de scène
  
  // Format de sortie
  '-f', 'flv',
  '-flvflags', 'no_duration_filesize',
  rtmpUrl,
];
```

### Paramètres Audio

```dart
final audioArgs = [
  // Source audio (microphone ou système)
  '-f', 'avfoundation',        // macOS
  '-i', ':0',                  // Microphone par défaut
  
  // Encodage audio
  '-c:a', 'aac',
  '-b:a', '128k',
  '-ar', '44100',
  '-ac', '2',
];
```

## Gestion des Erreurs

### Retry Automatique

```dart
class StreamingRetryManager {
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 5);
  
  static Future<void> startWithRetry(
    String rtmpUrl,
    StreamSettings settings,
  ) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        await StreamingService().startStreaming(
          rtmpUrl: rtmpUrl,
          streamSettings: settings,
        );
        return; // Succès
      } catch (e) {
        attempts++;
        if (attempts >= maxRetries) rethrow;
        
        await Future.delayed(retryDelay);
      }
    }
  }
}
```

### Monitoring de la Connexion

```dart
class StreamHealthMonitor {
  static Timer? _healthCheckTimer;
  
  static void startMonitoring(StreamingService service) {
    _healthCheckTimer = Timer.periodic(
      Duration(seconds: 10),
      (timer) async {
        final status = service.currentStatus;
        
        if (status.state == StreamState.error) {
          // Tentative de reconnexion
          await _attemptReconnection(service);
        }
      },
    );
  }
  
  static Future<void> _attemptReconnection(StreamingService service) async {
    try {
      await service.stopStreaming();
      await Future.delayed(Duration(seconds: 2));
      // Redémarrer avec les mêmes paramètres
    } catch (e) {
      Logger().e('Reconnection failed: $e');
    }
  }
}
```

## Permissions Requises

### Android (android/app/src/main/AndroidManifest.xml)

```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
```

### iOS (ios/Runner/Info.plist)

```xml
<key>NSCameraUsageDescription</key>
<string>Cette application a besoin d'accéder à la caméra pour le streaming en direct</string>
<key>NSMicrophoneUsageDescription</key>
<string>Cette application a besoin d'accéder au microphone pour le streaming en direct</string>
```

## Dépannage

### Problèmes Courants

1. **FFmpeg non trouvé** : Vérifiez que le package est correctement installé
2. **Permission refusée** : Assurez-vous que toutes les permissions sont accordées
3. **Échec de connexion RTMP** : Vérifiez l'URL et les paramètres du serveur
4. **Performance faible** : Réduisez la résolution ou le débit

### Logs de Debug

```dart
// Activer les logs détaillés
Logger.level = Level.debug;

// Surveiller les sessions FFmpeg
FFmpegKit.enableLogCallback((log) {
  print('FFmpeg: ${log.getMessage()}');
});
```

## Prochaines Étapes

1. Implémenter la capture d'écran native
2. Optimiser les paramètres FFmpeg
3. Ajouter la gestion audio
4. Implémenter la reconnexion automatique
5. Ajouter des métriques de performance
