import 'dart:async';
import 'package:uuid/uuid.dart';
import 'package:logger/logger.dart';

import '../constants/app_constants.dart';
import '../models/quiz_models.dart';
import '../models/youtube_models.dart';
import 'database_service.dart';
import 'youtube_chat_service.dart';

/// Service for managing quiz operations
class QuizService {
  static final Logger _logger = Logger();
  static const Uuid _uuid = Uuid();
  
  final DatabaseService _databaseService;
  final YouTubeChatService _chatService;
  
  LiveQuizSession? _currentSession;
  Timer? _questionTimer;
  final List<YouTubeChatMessage> _currentQuestionMessages = [];
  
  final StreamController<LiveQuizSession> _sessionController = 
      StreamController<LiveQuizSession>.broadcast();
  final StreamController<QuizResult> _resultController = 
      StreamController<QuizResult>.broadcast();
  
  QuizService(this._databaseService, this._chatService) {
    _chatService.chatMessages.listen(_handleChatMessages);
  }
  
  /// Stream of live quiz session updates
  Stream<LiveQuizSession> get sessionStream => _sessionController.stream;
  
  /// Stream of quiz results
  Stream<QuizResult> get resultStream => _resultController.stream;
  
  /// Current live session
  LiveQuizSession? get currentSession => _currentSession;
  
  /// Check if a quiz session is active
  bool get isSessionActive => _currentSession != null && 
      _currentSession!.status != QuizSessionStatus.completed;
  
  /// Create a new quiz
  Future<Quiz> createQuiz({
    required String title,
    required String description,
    required List<QuizQuestion> questions,
  }) async {
    try {
      final quiz = Quiz(
        id: _uuid.v4(),
        title: title,
        description: description,
        questions: questions,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      await _databaseService.saveQuiz(quiz);
      _logger.i('Created quiz: ${quiz.title}');
      
      return quiz;
    } catch (e) {
      _logger.e('Failed to create quiz: $e');
      rethrow;
    }
  }
  
  /// Update an existing quiz
  Future<Quiz> updateQuiz(Quiz quiz) async {
    try {
      final updatedQuiz = quiz.copyWith(updatedAt: DateTime.now());
      await _databaseService.saveQuiz(updatedQuiz);
      _logger.i('Updated quiz: ${quiz.title}');
      
      return updatedQuiz;
    } catch (e) {
      _logger.e('Failed to update quiz: $e');
      rethrow;
    }
  }
  
  /// Delete a quiz
  Future<void> deleteQuiz(String quizId) async {
    try {
      await _databaseService.deleteQuiz(quizId);
      _logger.i('Deleted quiz: $quizId');
    } catch (e) {
      _logger.e('Failed to delete quiz: $e');
      rethrow;
    }
  }
  
  /// Get all quizzes
  Future<List<Quiz>> getAllQuizzes() async {
    try {
      return await _databaseService.getAllQuizzes();
    } catch (e) {
      _logger.e('Failed to get quizzes: $e');
      rethrow;
    }
  }
  
  /// Get quiz by ID
  Future<Quiz?> getQuizById(String quizId) async {
    try {
      return await _databaseService.getQuizById(quizId);
    } catch (e) {
      _logger.e('Failed to get quiz: $e');
      rethrow;
    }
  }
  
  /// Start a live quiz session
  Future<void> startQuizSession(Quiz quiz) async {
    if (isSessionActive) {
      throw Exception('A quiz session is already active');
    }
    
    try {
      _currentSession = LiveQuizSession(
        id: _uuid.v4(),
        quiz: quiz,
        status: QuizSessionStatus.waiting,
        startedAt: DateTime.now(),
      );
      
      _sessionController.add(_currentSession!);
      _logger.i('Started quiz session: ${quiz.title}');
    } catch (e) {
      _logger.e('Failed to start quiz session: $e');
      rethrow;
    }
  }
  
  /// Start the next question in the session
  Future<void> startNextQuestion() async {
    if (!isSessionActive) {
      throw Exception('No active quiz session');
    }
    
    final session = _currentSession!;
    
    if (session.currentQuestionIndex >= session.quiz.questions.length) {
      await _completeSession();
      return;
    }
    
    try {
      final currentQuestion = session.quiz.questions[session.currentQuestionIndex];
      
      _currentSession = session.copyWith(
        status: QuizSessionStatus.questionActive,
        currentQuestionStartedAt: DateTime.now(),
      );
      
      _currentQuestionMessages.clear();
      _sessionController.add(_currentSession!);
      
      // Start question timer
      _questionTimer = Timer(
        Duration(seconds: currentQuestion.timeLimit),
        () => _completeCurrentQuestion(),
      );
      
      _logger.i('Started question ${session.currentQuestionIndex + 1}: ${currentQuestion.title}');
    } catch (e) {
      _logger.e('Failed to start next question: $e');
      rethrow;
    }
  }
  
  /// Complete the current question and process results
  Future<void> _completeCurrentQuestion() async {
    if (!isSessionActive || _currentSession!.status != QuizSessionStatus.questionActive) {
      return;
    }
    
    try {
      final session = _currentSession!;
      final currentQuestion = session.quiz.questions[session.currentQuestionIndex];
      final correctAnswer = AppConstants.answerLabels[currentQuestion.correctAnswerIndex];
      
      // Process chat messages for answers
      final answerMessages = _chatService.filterAnswerMessages(
        _currentQuestionMessages,
        AppConstants.answerLabels,
      );
      
      final answers = answerMessages.map((message) {
        final selectedOption = AppConstants.answerLabels.indexOf(
          message.messageText.trim().toUpperCase()
        );
        
        return QuizAnswer(
          id: _uuid.v4(),
          questionId: currentQuestion.id,
          participantName: message.authorDisplayName,
          participantId: message.authorChannelId,
          selectedOption: selectedOption,
          timestamp: message.publishedAt,
          isCorrect: selectedOption == currentQuestion.correctAnswerIndex,
        );
      }).toList();
      
      // Find winner (first correct answer)
      final winner = _chatService.findFirstCorrectAnswer(
        _currentQuestionMessages,
        correctAnswer,
      );
      
      QuizAnswer? winnerAnswer;
      if (winner != null) {
        winnerAnswer = QuizAnswer(
          id: _uuid.v4(),
          questionId: currentQuestion.id,
          participantName: winner.authorDisplayName,
          participantId: winner.authorChannelId,
          selectedOption: currentQuestion.correctAnswerIndex,
          timestamp: winner.publishedAt,
          isCorrect: true,
        );
      }
      
      // Get top 3 correct answers
      final topCorrectMessages = _chatService.getTopCorrectAnswers(
        _currentQuestionMessages,
        correctAnswer,
        3,
      );
      
      final topAnswers = topCorrectMessages.map((message) => QuizAnswer(
        id: _uuid.v4(),
        questionId: currentQuestion.id,
        participantName: message.authorDisplayName,
        participantId: message.authorChannelId,
        selectedOption: currentQuestion.correctAnswerIndex,
        timestamp: message.publishedAt,
        isCorrect: true,
      )).toList();
      
      // Create result
      final result = QuizResult(
        id: _uuid.v4(),
        quizId: session.quiz.id,
        questionId: currentQuestion.id,
        answers: answers,
        winner: winnerAnswer,
        topAnswers: topAnswers,
        completedAt: DateTime.now(),
      );
      
      // Update session
      final updatedResults = [...session.results, result];
      _currentSession = session.copyWith(
        status: QuizSessionStatus.questionCompleted,
        results: updatedResults,
        currentQuestionIndex: session.currentQuestionIndex + 1,
      );
      
      _sessionController.add(_currentSession!);
      _resultController.add(result);
      
      // Save result to database
      await _databaseService.saveQuizResult(result);
      
      _logger.i('Completed question ${session.currentQuestionIndex + 1}');
      _logger.i('Winner: ${winnerAnswer?.participantName ?? 'No winner'}');
      _logger.i('Total answers: ${answers.length}');
      
    } catch (e) {
      _logger.e('Failed to complete current question: $e');
    }
  }
  
  /// Skip the current question
  Future<void> skipCurrentQuestion() async {
    if (!isSessionActive || _currentSession!.status != QuizSessionStatus.questionActive) {
      return;
    }
    
    _questionTimer?.cancel();
    await _completeCurrentQuestion();
  }
  
  /// Complete the quiz session
  Future<void> _completeSession() async {
    if (!isSessionActive) return;
    
    try {
      _questionTimer?.cancel();
      
      _currentSession = _currentSession!.copyWith(
        status: QuizSessionStatus.completed,
      );
      
      _sessionController.add(_currentSession!);
      
      // Save session to database
      await _databaseService.saveQuizSession(_currentSession!);
      
      _logger.i('Completed quiz session: ${_currentSession!.quiz.title}');
      _currentSession = null;
    } catch (e) {
      _logger.e('Failed to complete session: $e');
    }
  }
  
  /// Stop the current quiz session
  Future<void> stopQuizSession() async {
    if (!isSessionActive) return;
    
    _questionTimer?.cancel();
    await _completeSession();
  }
  
  /// Handle incoming chat messages
  void _handleChatMessages(List<YouTubeChatMessage> messages) {
    if (isSessionActive && _currentSession!.status == QuizSessionStatus.questionActive) {
      _currentQuestionMessages.addAll(messages);
    }
  }
  
  /// Generate leaderboard for current session
  List<LeaderboardEntry> generateLeaderboard() {
    if (_currentSession == null) return [];
    
    final participantStats = <String, LeaderboardEntry>{};
    
    for (final result in _currentSession!.results) {
      for (final answer in result.answers) {
        final entry = participantStats[answer.participantId] ?? LeaderboardEntry(
          participantName: answer.participantName,
          participantId: answer.participantId,
        );
        
        participantStats[answer.participantId] = entry.copyWith(
          totalAnswers: entry.totalAnswers + 1,
          correctAnswers: entry.correctAnswers + (answer.isCorrect ? 1 : 0),
          points: entry.points + (answer.isCorrect ? 10 : 0),
        );
      }
    }
    
    final leaderboard = participantStats.values.toList();
    leaderboard.sort((a, b) => b.points.compareTo(a.points));
    
    return leaderboard;
  }
  
  /// Dispose resources
  void dispose() {
    _questionTimer?.cancel();
    _sessionController.close();
    _resultController.close();
  }
}
