import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:logger/logger.dart';

import '../constants/app_constants.dart';
import '../models/quiz_models.dart';

/// Service for local database operations
class DatabaseService {
  static final Logger _logger = Logger();
  static Database? _database;
  
  /// Get database instance
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }
  
  /// Initialize the database
  Future<Database> _initDatabase() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, AppConstants.databaseName);
      
      return await openDatabase(
        path,
        version: AppConstants.databaseVersion,
        onCreate: _createTables,
        onUpgrade: _upgradeDatabase,
      );
    } catch (e) {
      _logger.e('Failed to initialize database: $e');
      rethrow;
    }
  }
  
  /// Create database tables
  Future<void> _createTables(Database db, int version) async {
    try {
      // Quizzes table
      await db.execute('''
        CREATE TABLE quizzes (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          description TEXT NOT NULL,
          questions TEXT NOT NULL,
          is_active INTEGER DEFAULT 0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');
      
      // Quiz sessions table
      await db.execute('''
        CREATE TABLE quiz_sessions (
          id TEXT PRIMARY KEY,
          quiz_id TEXT NOT NULL,
          quiz_data TEXT NOT NULL,
          current_question_index INTEGER DEFAULT 0,
          results TEXT NOT NULL,
          status TEXT NOT NULL,
          started_at TEXT,
          current_question_started_at TEXT,
          FOREIGN KEY (quiz_id) REFERENCES quizzes (id)
        )
      ''');
      
      // Quiz results table
      await db.execute('''
        CREATE TABLE quiz_results (
          id TEXT PRIMARY KEY,
          quiz_id TEXT NOT NULL,
          question_id TEXT NOT NULL,
          answers TEXT NOT NULL,
          winner TEXT,
          top_answers TEXT NOT NULL,
          completed_at TEXT,
          FOREIGN KEY (quiz_id) REFERENCES quizzes (id)
        )
      ''');
      
      // Quiz answers table
      await db.execute('''
        CREATE TABLE quiz_answers (
          id TEXT PRIMARY KEY,
          question_id TEXT NOT NULL,
          participant_name TEXT NOT NULL,
          participant_id TEXT NOT NULL,
          selected_option INTEGER NOT NULL,
          is_correct INTEGER DEFAULT 0,
          timestamp TEXT NOT NULL
        )
      ''');
      
      // Leaderboard table
      await db.execute('''
        CREATE TABLE leaderboard (
          participant_id TEXT PRIMARY KEY,
          participant_name TEXT NOT NULL,
          correct_answers INTEGER DEFAULT 0,
          total_answers INTEGER DEFAULT 0,
          average_response_time REAL DEFAULT 0.0,
          points INTEGER DEFAULT 0,
          last_updated TEXT NOT NULL
        )
      ''');
      
      _logger.i('Database tables created successfully');
    } catch (e) {
      _logger.e('Failed to create database tables: $e');
      rethrow;
    }
  }
  
  /// Upgrade database schema
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    _logger.i('Upgrading database from version $oldVersion to $newVersion');
    // Add migration logic here when needed
  }
  
  /// Save a quiz to the database
  Future<void> saveQuiz(Quiz quiz) async {
    try {
      final db = await database;
      
      await db.insert(
        'quizzes',
        {
          'id': quiz.id,
          'title': quiz.title,
          'description': quiz.description,
          'questions': json.encode(quiz.questions.map((q) => q.toJson()).toList()),
          'is_active': quiz.isActive ? 1 : 0,
          'created_at': quiz.createdAt?.toIso8601String() ?? DateTime.now().toIso8601String(),
          'updated_at': quiz.updatedAt?.toIso8601String() ?? DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      _logger.d('Saved quiz: ${quiz.id}');
    } catch (e) {
      _logger.e('Failed to save quiz: $e');
      rethrow;
    }
  }
  
  /// Get all quizzes from the database
  Future<List<Quiz>> getAllQuizzes() async {
    try {
      final db = await database;
      final maps = await db.query('quizzes', orderBy: 'created_at DESC');
      
      return maps.map((map) => _mapToQuiz(map)).toList();
    } catch (e) {
      _logger.e('Failed to get all quizzes: $e');
      rethrow;
    }
  }
  
  /// Get a quiz by ID
  Future<Quiz?> getQuizById(String id) async {
    try {
      final db = await database;
      final maps = await db.query(
        'quizzes',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );
      
      if (maps.isNotEmpty) {
        return _mapToQuiz(maps.first);
      }
      
      return null;
    } catch (e) {
      _logger.e('Failed to get quiz by ID: $e');
      rethrow;
    }
  }
  
  /// Delete a quiz from the database
  Future<void> deleteQuiz(String id) async {
    try {
      final db = await database;
      
      // Delete related data first
      await db.delete('quiz_answers', where: 'question_id IN (SELECT id FROM quizzes WHERE id = ?)', whereArgs: [id]);
      await db.delete('quiz_results', where: 'quiz_id = ?', whereArgs: [id]);
      await db.delete('quiz_sessions', where: 'quiz_id = ?', whereArgs: [id]);
      
      // Delete the quiz
      await db.delete('quizzes', where: 'id = ?', whereArgs: [id]);
      
      _logger.d('Deleted quiz: $id');
    } catch (e) {
      _logger.e('Failed to delete quiz: $e');
      rethrow;
    }
  }
  
  /// Save a quiz session
  Future<void> saveQuizSession(LiveQuizSession session) async {
    try {
      final db = await database;
      
      await db.insert(
        'quiz_sessions',
        {
          'id': session.id,
          'quiz_id': session.quiz.id,
          'quiz_data': json.encode(session.quiz.toJson()),
          'current_question_index': session.currentQuestionIndex,
          'results': json.encode(session.results.map((r) => r.toJson()).toList()),
          'status': session.status.name,
          'started_at': session.startedAt?.toIso8601String(),
          'current_question_started_at': session.currentQuestionStartedAt?.toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      _logger.d('Saved quiz session: ${session.id}');
    } catch (e) {
      _logger.e('Failed to save quiz session: $e');
      rethrow;
    }
  }
  
  /// Save a quiz result
  Future<void> saveQuizResult(QuizResult result) async {
    try {
      final db = await database;
      
      await db.insert(
        'quiz_results',
        {
          'id': result.id,
          'quiz_id': result.quizId,
          'question_id': result.questionId,
          'answers': json.encode(result.answers.map((a) => a.toJson()).toList()),
          'winner': result.winner != null ? json.encode(result.winner!.toJson()) : null,
          'top_answers': json.encode(result.topAnswers.map((a) => a.toJson()).toList()),
          'completed_at': result.completedAt?.toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      // Save individual answers
      for (final answer in result.answers) {
        await _saveQuizAnswer(answer);
      }
      
      _logger.d('Saved quiz result: ${result.id}');
    } catch (e) {
      _logger.e('Failed to save quiz result: $e');
      rethrow;
    }
  }
  
  /// Save a quiz answer
  Future<void> _saveQuizAnswer(QuizAnswer answer) async {
    try {
      final db = await database;
      
      await db.insert(
        'quiz_answers',
        {
          'id': answer.id,
          'question_id': answer.questionId,
          'participant_name': answer.participantName,
          'participant_id': answer.participantId,
          'selected_option': answer.selectedOption,
          'is_correct': answer.isCorrect ? 1 : 0,
          'timestamp': answer.timestamp.toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      _logger.e('Failed to save quiz answer: $e');
      rethrow;
    }
  }
  
  /// Update leaderboard entry
  Future<void> updateLeaderboardEntry(LeaderboardEntry entry) async {
    try {
      final db = await database;
      
      await db.insert(
        'leaderboard',
        {
          'participant_id': entry.participantId,
          'participant_name': entry.participantName,
          'correct_answers': entry.correctAnswers,
          'total_answers': entry.totalAnswers,
          'average_response_time': entry.averageResponseTime,
          'points': entry.points,
          'last_updated': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      _logger.d('Updated leaderboard entry: ${entry.participantId}');
    } catch (e) {
      _logger.e('Failed to update leaderboard entry: $e');
      rethrow;
    }
  }
  
  /// Get leaderboard entries
  Future<List<LeaderboardEntry>> getLeaderboard({int limit = 10}) async {
    try {
      final db = await database;
      final maps = await db.query(
        'leaderboard',
        orderBy: 'points DESC, correct_answers DESC',
        limit: limit,
      );
      
      return maps.map((map) => _mapToLeaderboardEntry(map)).toList();
    } catch (e) {
      _logger.e('Failed to get leaderboard: $e');
      rethrow;
    }
  }
  
  /// Convert database map to Quiz object
  Quiz _mapToQuiz(Map<String, dynamic> map) {
    final questionsJson = json.decode(map['questions']) as List;
    final questions = questionsJson
        .map((q) => QuizQuestion.fromJson(q as Map<String, dynamic>))
        .toList();
    
    return Quiz(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      questions: questions,
      isActive: map['is_active'] == 1,
      createdAt: DateTime.tryParse(map['created_at'] ?? ''),
      updatedAt: DateTime.tryParse(map['updated_at'] ?? ''),
    );
  }
  
  /// Convert database map to LeaderboardEntry object
  LeaderboardEntry _mapToLeaderboardEntry(Map<String, dynamic> map) {
    return LeaderboardEntry(
      participantId: map['participant_id'],
      participantName: map['participant_name'],
      correctAnswers: map['correct_answers'],
      totalAnswers: map['total_answers'],
      averageResponseTime: map['average_response_time'],
      points: map['points'],
    );
  }
  
  /// Close the database
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
