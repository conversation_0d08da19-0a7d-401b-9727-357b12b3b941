allprojects {
    repositories {
        google()
        mavenCentral()
    }
    
    // Configuration simple pour mrljdx
    configurations.all {
        resolutionStrategy {
            force("com.mrljdx:ffmpeg-kit-full:6.0")
            
            dependencySubstitution {
                substitute(module("com.arthenica:ffmpeg-kit-https:6.0-2"))
                    .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
                substitute(module("com.arthenica:ffmpeg-kit-https"))
                    .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
                substitute(module("com.arthenica:ffmpeg-kit-android-full"))
                    .using(module("com.mrljdx:ffmpeg-kit-full:6.0"))
            }
        }
    }
}

val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    project.layout.buildDirectory.value(newBuildDir.dir(project.name))
}
