/// Validation utilities for forms and inputs
class Validators {
  /// Validate email address
  static String? email(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }

  /// Validate required field
  static String? required(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }
    return null;
  }

  /// Validate minimum length
  static String? minLength(String? value, int minLength, [String? fieldName]) {
    if (value == null || value.length < minLength) {
      return '${fieldName ?? 'This field'} must be at least $minLength characters';
    }
    return null;
  }

  /// Validate maximum length
  static String? maxLength(String? value, int maxLength, [String? fieldName]) {
    if (value != null && value.length > maxLength) {
      return '${fieldName ?? 'This field'} must be no more than $maxLength characters';
    }
    return null;
  }

  /// Validate URL
  static String? url(String? value) {
    if (value == null || value.isEmpty) {
      return 'URL is required';
    }
    
    final urlRegex = RegExp(r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$');
    if (!urlRegex.hasMatch(value)) {
      return 'Please enter a valid URL';
    }
    
    return null;
  }

  /// Validate YouTube stream key
  static String? youtubeStreamKey(String? value) {
    if (value == null || value.isEmpty) {
      return 'Stream key is required';
    }
    
    // YouTube stream keys are typically 20-40 characters long
    if (value.length < 10 || value.length > 50) {
      return 'Invalid stream key format';
    }
    
    return null;
  }

  /// Validate quiz title
  static String? quizTitle(String? value) {
    final requiredValidation = required(value, 'Quiz title');
    if (requiredValidation != null) return requiredValidation;
    
    final lengthValidation = minLength(value, 3, 'Quiz title');
    if (lengthValidation != null) return lengthValidation;
    
    final maxLengthValidation = maxLength(value, 100, 'Quiz title');
    if (maxLengthValidation != null) return maxLengthValidation;
    
    return null;
  }

  /// Validate question text
  static String? questionText(String? value) {
    final requiredValidation = required(value, 'Question text');
    if (requiredValidation != null) return requiredValidation;
    
    final lengthValidation = minLength(value, 10, 'Question text');
    if (lengthValidation != null) return lengthValidation;
    
    final maxLengthValidation = maxLength(value, 500, 'Question text');
    if (maxLengthValidation != null) return maxLengthValidation;
    
    return null;
  }

  /// Validate answer option
  static String? answerOption(String? value) {
    final requiredValidation = required(value, 'Answer option');
    if (requiredValidation != null) return requiredValidation;
    
    final lengthValidation = minLength(value, 1, 'Answer option');
    if (lengthValidation != null) return lengthValidation;
    
    final maxLengthValidation = maxLength(value, 200, 'Answer option');
    if (maxLengthValidation != null) return maxLengthValidation;
    
    return null;
  }

  /// Validate time limit
  static String? timeLimit(String? value) {
    if (value == null || value.isEmpty) {
      return 'Time limit is required';
    }
    
    final intValue = int.tryParse(value);
    if (intValue == null) {
      return 'Please enter a valid number';
    }
    
    if (intValue < 5) {
      return 'Time limit must be at least 5 seconds';
    }
    
    if (intValue > 300) {
      return 'Time limit must be no more than 300 seconds';
    }
    
    return null;
  }

  /// Validate bitrate
  static String? bitrate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Bitrate is required';
    }
    
    final intValue = int.tryParse(value);
    if (intValue == null) {
      return 'Please enter a valid number';
    }
    
    if (intValue < 500000) {
      return 'Bitrate must be at least 500 Kbps';
    }
    
    if (intValue > 10000000) {
      return 'Bitrate must be no more than 10 Mbps';
    }
    
    return null;
  }

  /// Combine multiple validators
  static String? combine(List<String? Function()> validators) {
    for (final validator in validators) {
      final result = validator();
      if (result != null) return result;
    }
    return null;
  }
}
